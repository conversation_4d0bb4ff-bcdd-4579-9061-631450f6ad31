/**
 * Calculate the total value of a portfolio item
 * @param {number} amount - Amount of cryptocurrency held
 * @param {number} currentPrice - Current price per unit
 * @returns {number} Total value
 */
export const calculateItemValue = (amount, currentPrice) => {
  if (!amount || !currentPrice || isNaN(amount) || isNaN(currentPrice)) {
    return 0;
  }
  return amount * currentPrice;
};

/**
 * Calculate the total portfolio value
 * @param {Array} portfolio - Array of portfolio items
 * @returns {number} Total portfolio value
 */
export const calculateTotalPortfolioValue = (portfolio) => {
  if (!Array.isArray(portfolio) || portfolio.length === 0) {
    return 0;
  }

  return portfolio.reduce((total, item) => {
    const itemValue = calculateItemValue(item.amount, item.currentPrice);
    return total + itemValue;
  }, 0);
};

/**
 * Calculate portfolio allocation percentages
 * @param {Array} portfolio - Array of portfolio items
 * @returns {Array} Portfolio items with allocation percentages
 */
export const calculatePortfolioAllocation = (portfolio) => {
  if (!Array.isArray(portfolio) || portfolio.length === 0) {
    return [];
  }

  const totalValue = calculateTotalPortfolioValue(portfolio);
  
  if (totalValue === 0) {
    return portfolio.map(item => ({
      ...item,
      allocation: 0,
      value: 0,
    }));
  }

  return portfolio.map(item => {
    const value = calculateItemValue(item.amount, item.currentPrice);
    const allocation = (value / totalValue) * 100;
    
    return {
      ...item,
      value,
      allocation,
    };
  });
};

/**
 * Calculate 24h portfolio change
 * @param {Array} portfolio - Array of portfolio items
 * @returns {object} Change amount and percentage
 */
export const calculatePortfolioChange = (portfolio) => {
  if (!Array.isArray(portfolio) || portfolio.length === 0) {
    return { changeAmount: 0, changePercentage: 0 };
  }

  let currentValue = 0;
  let previousValue = 0;

  portfolio.forEach(item => {
    const itemCurrentValue = calculateItemValue(item.amount, item.currentPrice);
    const previousPrice = item.currentPrice / (1 + (item.priceChange24h / 100));
    const itemPreviousValue = calculateItemValue(item.amount, previousPrice);
    
    currentValue += itemCurrentValue;
    previousValue += itemPreviousValue;
  });

  const changeAmount = currentValue - previousValue;
  const changePercentage = previousValue !== 0 ? (changeAmount / previousValue) * 100 : 0;

  return {
    changeAmount,
    changePercentage,
    currentValue,
    previousValue,
  };
};

/**
 * Calculate profit/loss for a portfolio item
 * @param {number} amount - Amount held
 * @param {number} currentPrice - Current price
 * @param {number} buyPrice - Original buy price (optional)
 * @returns {object} Profit/loss data
 */
export const calculateProfitLoss = (amount, currentPrice, buyPrice = null) => {
  if (!amount || !currentPrice || isNaN(amount) || isNaN(currentPrice)) {
    return {
      currentValue: 0,
      originalValue: 0,
      profitLoss: 0,
      profitLossPercentage: 0,
    };
  }

  const currentValue = calculateItemValue(amount, currentPrice);
  
  if (!buyPrice || isNaN(buyPrice)) {
    return {
      currentValue,
      originalValue: 0,
      profitLoss: 0,
      profitLossPercentage: 0,
    };
  }

  const originalValue = calculateItemValue(amount, buyPrice);
  const profitLoss = currentValue - originalValue;
  const profitLossPercentage = originalValue !== 0 ? (profitLoss / originalValue) * 100 : 0;

  return {
    currentValue,
    originalValue,
    profitLoss,
    profitLossPercentage,
  };
};

/**
 * Calculate portfolio statistics
 * @param {Array} portfolio - Array of portfolio items
 * @returns {object} Portfolio statistics
 */
export const calculatePortfolioStats = (portfolio) => {
  if (!Array.isArray(portfolio) || portfolio.length === 0) {
    return {
      totalValue: 0,
      totalItems: 0,
      topPerformer: null,
      worstPerformer: null,
      averageChange: 0,
      totalChange: { changeAmount: 0, changePercentage: 0 },
    };
  }

  const totalValue = calculateTotalPortfolioValue(portfolio);
  const totalChange = calculatePortfolioChange(portfolio);
  
  // Find top and worst performers
  let topPerformer = null;
  let worstPerformer = null;
  let totalChangeSum = 0;

  portfolio.forEach(item => {
    if (!topPerformer || item.priceChange24h > topPerformer.priceChange24h) {
      topPerformer = item;
    }
    if (!worstPerformer || item.priceChange24h < worstPerformer.priceChange24h) {
      worstPerformer = item;
    }
    totalChangeSum += item.priceChange24h;
  });

  const averageChange = portfolio.length > 0 ? totalChangeSum / portfolio.length : 0;

  return {
    totalValue,
    totalItems: portfolio.length,
    topPerformer,
    worstPerformer,
    averageChange,
    totalChange,
  };
};

/**
 * Calculate diversification score (0-100)
 * @param {Array} portfolio - Array of portfolio items with allocations
 * @returns {number} Diversification score
 */
export const calculateDiversificationScore = (portfolio) => {
  if (!Array.isArray(portfolio) || portfolio.length === 0) {
    return 0;
  }

  if (portfolio.length === 1) {
    return 0; // No diversification with only one asset
  }

  const allocations = calculatePortfolioAllocation(portfolio);
  
  // Calculate Herfindahl-Hirschman Index (HHI)
  const hhi = allocations.reduce((sum, item) => {
    const allocation = item.allocation / 100; // Convert to decimal
    return sum + (allocation * allocation);
  }, 0);

  // Convert HHI to diversification score (0-100)
  // HHI ranges from 1/n to 1, where n is number of assets
  const minHHI = 1 / portfolio.length;
  const maxHHI = 1;
  
  const normalizedHHI = (hhi - minHHI) / (maxHHI - minHHI);
  const diversificationScore = (1 - normalizedHHI) * 100;

  return Math.max(0, Math.min(100, diversificationScore));
};

/**
 * Calculate rebalancing suggestions
 * @param {Array} portfolio - Array of portfolio items
 * @param {number} targetAllocation - Target allocation percentage for each item
 * @returns {Array} Rebalancing suggestions
 */
export const calculateRebalancingSuggestions = (portfolio, targetAllocation = null) => {
  if (!Array.isArray(portfolio) || portfolio.length === 0) {
    return [];
  }

  const allocatedPortfolio = calculatePortfolioAllocation(portfolio);
  const evenAllocation = targetAllocation || (100 / portfolio.length);

  return allocatedPortfolio.map(item => {
    const currentAllocation = item.allocation;
    const difference = currentAllocation - evenAllocation;
    const action = difference > 5 ? 'SELL' : difference < -5 ? 'BUY' : 'HOLD';
    
    return {
      ...item,
      targetAllocation: evenAllocation,
      currentAllocation,
      difference,
      action,
      suggested: Math.abs(difference) > 5,
    };
  });
};

/**
 * Calculate compound annual growth rate (CAGR)
 * @param {number} beginningValue - Initial value
 * @param {number} endingValue - Final value
 * @param {number} periods - Number of periods (years)
 * @returns {number} CAGR percentage
 */
export const calculateCAGR = (beginningValue, endingValue, periods) => {
  if (!beginningValue || !endingValue || !periods || 
      beginningValue <= 0 || endingValue <= 0 || periods <= 0) {
    return 0;
  }

  const cagr = (Math.pow(endingValue / beginningValue, 1 / periods) - 1) * 100;
  return isFinite(cagr) ? cagr : 0;
};

/**
 * Calculate risk metrics (simplified)
 * @param {Array} priceHistory - Array of historical prices
 * @returns {object} Risk metrics
 */
export const calculateRiskMetrics = (priceHistory) => {
  if (!Array.isArray(priceHistory) || priceHistory.length < 2) {
    return {
      volatility: 0,
      maxDrawdown: 0,
      sharpeRatio: 0,
    };
  }

  // Calculate daily returns
  const returns = [];
  for (let i = 1; i < priceHistory.length; i++) {
    const dailyReturn = (priceHistory[i] - priceHistory[i - 1]) / priceHistory[i - 1];
    returns.push(dailyReturn);
  }

  // Calculate volatility (standard deviation of returns)
  const meanReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
  const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - meanReturn, 2), 0) / returns.length;
  const volatility = Math.sqrt(variance) * Math.sqrt(365) * 100; // Annualized volatility

  // Calculate maximum drawdown
  let maxDrawdown = 0;
  let peak = priceHistory[0];
  
  for (let i = 1; i < priceHistory.length; i++) {
    if (priceHistory[i] > peak) {
      peak = priceHistory[i];
    }
    const drawdown = (peak - priceHistory[i]) / peak;
    if (drawdown > maxDrawdown) {
      maxDrawdown = drawdown;
    }
  }

  // Simplified Sharpe ratio (assuming risk-free rate of 2%)
  const riskFreeRate = 0.02;
  const excessReturn = (meanReturn * 365) - riskFreeRate;
  const sharpeRatio = volatility !== 0 ? excessReturn / (volatility / 100) : 0;

  return {
    volatility: isFinite(volatility) ? volatility : 0,
    maxDrawdown: isFinite(maxDrawdown) ? maxDrawdown * 100 : 0,
    sharpeRatio: isFinite(sharpeRatio) ? sharpeRatio : 0,
  };
};
