import axios from 'axios';
import { 
  API_CONFIG, 
  API_ENDPOINTS, 
  ERROR_MESSAGES, 
  TIME_INTERVALS,
  VALIDATION_RULES 
} from '../utils/constants.js';

/**
 * CoinGecko API Service
 * Handles all interactions with the CoinGecko API with advanced rate limiting
 */
class CoinGeckoAPI {
  constructor() {
    this.baseURL = API_CONFIG.COINGECKO_BASE_URL;
    this.requestQueue = [];
    this.isProcessingQueue = false;
    this.lastRequestTime = 0;
    this.requestCount = 0;
    this.windowStart = Date.now();
    this.maxRequestsPerMinute = 30; // Conservative limit for free tier
    this.retryAttempts = 3;
    this.retryDelay = 1000; // 1 second base delay
    
    // Create axios instance with default config
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: API_CONFIG.REQUEST_TIMEOUT,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
    });

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => this.handleError(error)
    );
  }

  /**
   * Handle API errors
   * @param {Error} error - Axios error object
   * @returns {Promise} Rejected promise with formatted error
   */
  handleError(error) {
    if (error.code === 'ECONNABORTED') {
      return Promise.reject(new Error('Request timeout. Please try again.'));
    }
    
    if (!error.response) {
      return Promise.reject(new Error(ERROR_MESSAGES.NETWORK_ERROR));
    }

    const { status, data } = error.response;
    
    switch (status) {
      case 429:
        return Promise.reject(new Error('Rate limit exceeded. Please wait a moment and try again.'));
      case 404:
        return Promise.reject(new Error('Cryptocurrency not found.'));
      case 500:
      case 502:
      case 503:
        return Promise.reject(new Error('CoinGecko service is temporarily unavailable.'));
      default:
        return Promise.reject(new Error(data?.error || ERROR_MESSAGES.API_ERROR));
    }
  }

  /**
   * Advanced rate limiting mechanism with sliding window
   * @returns {Promise} Promise that resolves after rate limit delay
   */
  async rateLimit() {
    const now = Date.now();
    const windowDuration = 60000; // 1 minute window

    // Reset window if needed
    if (now - this.windowStart >= windowDuration) {
      this.requestCount = 0;
      this.windowStart = now;
    }

    // Check if we've exceeded the rate limit
    if (this.requestCount >= this.maxRequestsPerMinute) {
      const waitTime = windowDuration - (now - this.windowStart);
      console.warn(`Rate limit reached. Waiting ${waitTime}ms before next request.`);
      await new Promise(resolve => setTimeout(resolve, waitTime));

      // Reset after waiting
      this.requestCount = 0;
      this.windowStart = Date.now();
    }

    // Ensure minimum delay between requests
    const timeSinceLastRequest = now - this.lastRequestTime;
    if (timeSinceLastRequest < API_CONFIG.RATE_LIMIT_DELAY) {
      const delay = API_CONFIG.RATE_LIMIT_DELAY - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    this.requestCount++;
    this.lastRequestTime = Date.now();
  }

  /**
   * Retry mechanism with exponential backoff
   * @param {Function} requestFn - Function that makes the request
   * @param {number} attempt - Current attempt number
   * @returns {Promise} Request result
   */
  async retryWithBackoff(requestFn, attempt = 1) {
    try {
      return await requestFn();
    } catch (error) {
      if (attempt >= this.retryAttempts) {
        throw error;
      }

      // Check if it's a rate limit error
      if (error.response?.status === 429) {
        const retryAfter = error.response.headers['retry-after'];
        const delay = retryAfter ? parseInt(retryAfter) * 1000 : this.retryDelay * Math.pow(2, attempt - 1);

        console.warn(`Rate limited. Retrying after ${delay}ms (attempt ${attempt}/${this.retryAttempts})`);
        await new Promise(resolve => setTimeout(resolve, delay));

        return this.retryWithBackoff(requestFn, attempt + 1);
      }

      // For other errors, use exponential backoff
      if (error.response?.status >= 500 || !error.response) {
        const delay = this.retryDelay * Math.pow(2, attempt - 1);
        console.warn(`Request failed. Retrying after ${delay}ms (attempt ${attempt}/${this.retryAttempts})`);
        await new Promise(resolve => setTimeout(resolve, delay));

        return this.retryWithBackoff(requestFn, attempt + 1);
      }

      // Don't retry for client errors (4xx except 429)
      throw error;
    }
  }

  /**
   * Make a rate-limited API request with retry logic
   * @param {string} endpoint - API endpoint
   * @param {Object} params - Query parameters
   * @returns {Promise} API response data
   */
  async makeRequest(endpoint, params = {}) {
    return this.retryWithBackoff(async () => {
      await this.rateLimit();

      try {
        const response = await this.client.get(endpoint, { params });
        return response.data;
      } catch (error) {
        console.error(`API request failed for ${endpoint}:`, error.message);
        throw error;
      }
    });
  }

  /**
   * Get current rate limit status
   * @returns {Object} Rate limit information
   */
  getRateLimitStatus() {
    const now = Date.now();
    const windowRemaining = 60000 - (now - this.windowStart);
    const requestsRemaining = Math.max(0, this.maxRequestsPerMinute - this.requestCount);

    return {
      requestsRemaining,
      windowRemaining,
      requestCount: this.requestCount,
      maxRequestsPerMinute: this.maxRequestsPerMinute,
      nextResetTime: new Date(this.windowStart + 60000),
    };
  }

  /**
   * Search for cryptocurrencies
   * @param {string} query - Search query
   * @returns {Promise<Array>} Search results
   */
  async searchCoins(query) {
    if (!query || query.trim().length < 2) {
      return [];
    }

    try {
      const data = await this.makeRequest(API_ENDPOINTS.SEARCH, { query: query.trim() });
      
      // Return coins with additional filtering
      return (data.coins || [])
        .slice(0, VALIDATION_RULES.MAX_SEARCH_RESULTS)
        .map(coin => ({
          id: coin.id,
          name: coin.name,
          symbol: coin.symbol,
          thumb: coin.thumb,
          large: coin.large,
          market_cap_rank: coin.market_cap_rank,
        }));
    } catch (error) {
      console.error('Search coins error:', error);
      throw new Error('Failed to search cryptocurrencies. Please try again.');
    }
  }

  /**
   * Get cryptocurrency market data
   * @param {Array} coinIds - Array of coin IDs
   * @param {string} currency - Currency for prices (default: 'usd')
   * @returns {Promise<Array>} Market data for coins
   */
  async getCoinsMarketData(coinIds, currency = 'usd') {
    if (!Array.isArray(coinIds) || coinIds.length === 0) {
      return [];
    }

    try {
      const params = {
        ids: coinIds.join(','),
        vs_currency: currency,
        order: 'market_cap_desc',
        per_page: coinIds.length,
        page: 1,
        sparkline: false,
        price_change_percentage: '24h',
      };

      const data = await this.makeRequest(API_ENDPOINTS.COINS_MARKETS, params);
      
      return (data || []).map(coin => ({
        id: coin.id,
        symbol: coin.symbol,
        name: coin.name,
        image: coin.image,
        current_price: coin.current_price,
        market_cap: coin.market_cap,
        market_cap_rank: coin.market_cap_rank,
        fully_diluted_valuation: coin.fully_diluted_valuation,
        total_volume: coin.total_volume,
        high_24h: coin.high_24h,
        low_24h: coin.low_24h,
        price_change_24h: coin.price_change_24h,
        price_change_percentage_24h: coin.price_change_percentage_24h,
        market_cap_change_24h: coin.market_cap_change_24h,
        market_cap_change_percentage_24h: coin.market_cap_change_percentage_24h,
        circulating_supply: coin.circulating_supply,
        total_supply: coin.total_supply,
        max_supply: coin.max_supply,
        ath: coin.ath,
        ath_change_percentage: coin.ath_change_percentage,
        ath_date: coin.ath_date,
        atl: coin.atl,
        atl_change_percentage: coin.atl_change_percentage,
        atl_date: coin.atl_date,
        last_updated: coin.last_updated,
      }));
    } catch (error) {
      console.error('Get coins market data error:', error);
      throw new Error('Failed to fetch market data. Please try again.');
    }
  }

  /**
   * Get simple price data for multiple coins
   * @param {Array} coinIds - Array of coin IDs
   * @param {string} currency - Currency for prices (default: 'usd')
   * @param {boolean} includeChange - Include 24h change data
   * @returns {Promise<Object>} Price data object
   */
  async getSimplePrices(coinIds, currency = 'usd', includeChange = true) {
    if (!Array.isArray(coinIds) || coinIds.length === 0) {
      return {};
    }

    try {
      const params = {
        ids: coinIds.join(','),
        vs_currencies: currency,
      };

      if (includeChange) {
        params.include_24hr_change = true;
      }

      const data = await this.makeRequest(API_ENDPOINTS.SIMPLE_PRICE, params);
      return data || {};
    } catch (error) {
      console.error('Get simple prices error:', error);
      throw new Error('Failed to fetch price data. Please try again.');
    }
  }

  /**
   * Get detailed information about a specific cryptocurrency
   * @param {string} coinId - Coin ID
   * @param {boolean} localization - Include localized data
   * @param {boolean} tickers - Include ticker data
   * @param {boolean} marketData - Include market data
   * @param {boolean} communityData - Include community data
   * @param {boolean} developerData - Include developer data
   * @returns {Promise<Object>} Detailed coin information
   */
  async getCoinDetails(coinId, options = {}) {
    if (!coinId) {
      throw new Error('Coin ID is required');
    }

    const {
      localization = false,
      tickers = false,
      marketData = true,
      communityData = false,
      developerData = false,
    } = options;

    try {
      const params = {
        localization,
        tickers,
        market_data: marketData,
        community_data: communityData,
        developer_data: developerData,
      };

      const data = await this.makeRequest(`${API_ENDPOINTS.COIN_DETAIL}/${coinId}`, params);
      
      return {
        id: data.id,
        symbol: data.symbol,
        name: data.name,
        description: data.description,
        image: data.image,
        market_cap_rank: data.market_cap_rank,
        market_data: data.market_data,
        last_updated: data.last_updated,
        categories: data.categories,
        links: data.links,
      };
    } catch (error) {
      console.error('Get coin details error:', error);
      throw new Error(`Failed to fetch details for ${coinId}. Please try again.`);
    }
  }

  /**
   * Get trending cryptocurrencies
   * @returns {Promise<Array>} Trending coins
   */
  async getTrendingCoins() {
    try {
      const data = await this.makeRequest('/search/trending');
      
      return (data.coins || []).map(item => ({
        id: item.item.id,
        name: item.item.name,
        symbol: item.item.symbol,
        thumb: item.item.thumb,
        small: item.item.small,
        large: item.item.large,
        market_cap_rank: item.item.market_cap_rank,
        score: item.item.score,
      }));
    } catch (error) {
      console.error('Get trending coins error:', error);
      throw new Error('Failed to fetch trending cryptocurrencies.');
    }
  }

  /**
   * Get global cryptocurrency market data
   * @returns {Promise<Object>} Global market data
   */
  async getGlobalData() {
    try {
      const data = await this.makeRequest('/global');
      
      return {
        active_cryptocurrencies: data.data.active_cryptocurrencies,
        upcoming_icos: data.data.upcoming_icos,
        ongoing_icos: data.data.ongoing_icos,
        ended_icos: data.data.ended_icos,
        markets: data.data.markets,
        total_market_cap: data.data.total_market_cap,
        total_volume: data.data.total_volume,
        market_cap_percentage: data.data.market_cap_percentage,
        market_cap_change_percentage_24h_usd: data.data.market_cap_change_percentage_24h_usd,
        updated_at: data.data.updated_at,
      };
    } catch (error) {
      console.error('Get global data error:', error);
      throw new Error('Failed to fetch global market data.');
    }
  }

  /**
   * Get supported currencies
   * @returns {Promise<Array>} Supported currencies
   */
  async getSupportedCurrencies() {
    try {
      const data = await this.makeRequest('/simple/supported_vs_currencies');
      return data || [];
    } catch (error) {
      console.error('Get supported currencies error:', error);
      throw new Error('Failed to fetch supported currencies.');
    }
  }

  /**
   * Batch request handler for multiple API calls
   * @param {Array} requests - Array of request objects
   * @returns {Promise<Array>} Array of results
   */
  async batchRequests(requests) {
    if (!Array.isArray(requests) || requests.length === 0) {
      return [];
    }

    try {
      const results = await Promise.allSettled(
        requests.map(async (request) => {
          const { endpoint, params } = request;
          return await this.makeRequest(endpoint, params);
        })
      );

      return results.map((result, index) => ({
        success: result.status === 'fulfilled',
        data: result.status === 'fulfilled' ? result.value : null,
        error: result.status === 'rejected' ? result.reason.message : null,
        request: requests[index],
      }));
    } catch (error) {
      console.error('Batch requests error:', error);
      throw new Error('Failed to process batch requests.');
    }
  }
}

// Create and export a singleton instance
const coinGeckoAPI = new CoinGeckoAPI();
export default coinGeckoAPI;
