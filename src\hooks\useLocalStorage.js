import { useState, useEffect, useCallback } from 'react';
import { STORAGE_KEYS, ERROR_MESSAGES } from '../utils/constants.js';

/**
 * Custom hook for managing localStorage with React state
 * @param {string} key - Storage key
 * @param {*} initialValue - Initial value if nothing in storage
 * @returns {Array} [value, setValue, removeValue, error]
 */
export const useLocalStorage = (key, initialValue) => {
  const [storedValue, setStoredValue] = useState(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const [error, setError] = useState(null);

  const setValue = useCallback((value) => {
    try {
      setError(null);
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
      setError(ERROR_MESSAGES.STORAGE_ERROR);
    }
  }, [key, storedValue]);

  const removeValue = useCallback(() => {
    try {
      setError(null);
      window.localStorage.removeItem(key);
      setStoredValue(initialValue);
    } catch (error) {
      console.error(`Error removing localStorage key "${key}":`, error);
      setError(ERROR_MESSAGES.STORAGE_ERROR);
    }
  }, [key, initialValue]);

  // Listen for storage changes from other tabs
  useEffect(() => {
    const handleStorageChange = (e) => {
      if (e.key === key && e.newValue !== null) {
        try {
          setStoredValue(JSON.parse(e.newValue));
        } catch (error) {
          console.error(`Error parsing storage change for key "${key}":`, error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [key]);

  return [storedValue, setValue, removeValue, error];
};

/**
 * Hook specifically for portfolio storage
 * @returns {Array} [portfolio, setPortfolio, clearPortfolio, error]
 */
export const usePortfolioStorage = () => {
  return useLocalStorage(STORAGE_KEYS.PORTFOLIO, []);
};

/**
 * Hook for app settings storage
 * @returns {Array} [settings, setSettings, clearSettings, error]
 */
export const useSettingsStorage = () => {
  const defaultSettings = {
    currency: 'usd',
    theme: 'light',
    autoRefresh: true,
    refreshInterval: 60000,
    notifications: true,
    compactView: false,
  };

  return useLocalStorage(STORAGE_KEYS.SETTINGS, defaultSettings);
};

/**
 * Hook for caching API responses
 * @returns {Array} [cache, setCache, clearCache, error]
 */
export const useCacheStorage = () => {
  return useLocalStorage(STORAGE_KEYS.CACHE, {});
};

/**
 * Utility function to check localStorage availability
 * @returns {boolean} Whether localStorage is available
 */
export const isLocalStorageAvailable = () => {
  try {
    const test = '__localStorage_test__';
    window.localStorage.setItem(test, test);
    window.localStorage.removeItem(test);
    return true;
  } catch (error) {
    return false;
  }
};

/**
 * Utility function to get storage usage
 * @returns {object} Storage usage information
 */
export const getStorageUsage = () => {
  if (!isLocalStorageAvailable()) {
    return { used: 0, available: 0, percentage: 0 };
  }

  try {
    let used = 0;
    for (let key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        used += localStorage[key].length + key.length;
      }
    }

    // Estimate available space (most browsers limit to ~5-10MB)
    const estimated = 5 * 1024 * 1024; // 5MB estimate
    const percentage = (used / estimated) * 100;

    return {
      used,
      available: estimated - used,
      percentage: Math.min(percentage, 100),
    };
  } catch (error) {
    console.error('Error calculating storage usage:', error);
    return { used: 0, available: 0, percentage: 0 };
  }
};

/**
 * Utility function to clear all app-related storage
 * @returns {boolean} Success status
 */
export const clearAllAppStorage = () => {
  try {
    Object.values(STORAGE_KEYS).forEach(key => {
      window.localStorage.removeItem(key);
    });
    return true;
  } catch (error) {
    console.error('Error clearing app storage:', error);
    return false;
  }
};

/**
 * Hook for managing storage with expiration
 * @param {string} key - Storage key
 * @param {*} initialValue - Initial value
 * @param {number} ttl - Time to live in milliseconds
 * @returns {Array} [value, setValue, removeValue, error, isExpired]
 */
export const useLocalStorageWithExpiry = (key, initialValue, ttl = 300000) => {
  const [data, setData] = useState(() => {
    try {
      const item = window.localStorage.getItem(key);
      if (!item) return { value: initialValue, timestamp: Date.now() };

      const parsed = JSON.parse(item);
      const isExpired = Date.now() - parsed.timestamp > ttl;
      
      if (isExpired) {
        window.localStorage.removeItem(key);
        return { value: initialValue, timestamp: Date.now() };
      }

      return parsed;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return { value: initialValue, timestamp: Date.now() };
    }
  });

  const [error, setError] = useState(null);

  const setValue = useCallback((value) => {
    try {
      setError(null);
      const valueToStore = value instanceof Function ? value(data.value) : value;
      const dataToStore = {
        value: valueToStore,
        timestamp: Date.now(),
      };
      
      setData(dataToStore);
      window.localStorage.setItem(key, JSON.stringify(dataToStore));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
      setError(ERROR_MESSAGES.STORAGE_ERROR);
    }
  }, [key, data.value]);

  const removeValue = useCallback(() => {
    try {
      setError(null);
      window.localStorage.removeItem(key);
      setData({ value: initialValue, timestamp: Date.now() });
    } catch (error) {
      console.error(`Error removing localStorage key "${key}":`, error);
      setError(ERROR_MESSAGES.STORAGE_ERROR);
    }
  }, [key, initialValue]);

  const isExpired = Date.now() - data.timestamp > ttl;

  // Auto-remove expired data
  useEffect(() => {
    if (isExpired) {
      removeValue();
    }
  }, [isExpired, removeValue]);

  return [data.value, setValue, removeValue, error, isExpired];
};
