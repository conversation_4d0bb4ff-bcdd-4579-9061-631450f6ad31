import { APP_CONFIG } from './constants.js';

/**
 * Error Logger Utility
 * Centralized error logging and reporting system
 */
class ErrorLogger {
  constructor() {
    this.errors = [];
    this.maxErrors = 100;
    this.isProduction = import.meta.env.PROD;
    this.enableConsoleLogging = !this.isProduction || import.meta.env.VITE_DEBUG_MODE === 'true';
    this.enableRemoteLogging = import.meta.env.VITE_ENABLE_ERROR_REPORTING === 'true';
    
    // Initialize error tracking
    this.initializeErrorTracking();
  }

  /**
   * Initialize global error tracking
   */
  initializeErrorTracking() {
    // Catch unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.logError({
        type: 'unhandledrejection',
        message: event.reason?.message || 'Unhandled promise rejection',
        stack: event.reason?.stack,
        timestamp: new Date().toISOString(),
        url: window.location.href,
        userAgent: navigator.userAgent,
      });
    });

    // Catch global JavaScript errors
    window.addEventListener('error', (event) => {
      this.logError({
        type: 'javascript',
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack,
        timestamp: new Date().toISOString(),
        url: window.location.href,
        userAgent: navigator.userAgent,
      });
    });

    // Catch resource loading errors
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        this.logError({
          type: 'resource',
          message: `Failed to load resource: ${event.target.src || event.target.href}`,
          element: event.target.tagName,
          timestamp: new Date().toISOString(),
          url: window.location.href,
        });
      }
    }, true);
  }

  /**
   * Log an error with context information
   * @param {Error|Object} error - Error object or error details
   * @param {Object} context - Additional context information
   * @param {string} severity - Error severity level
   */
  logError(error, context = {}, severity = 'error') {
    const errorEntry = this.formatError(error, context, severity);
    
    // Add to local error store
    this.addToErrorStore(errorEntry);
    
    // Console logging
    if (this.enableConsoleLogging) {
      this.logToConsole(errorEntry);
    }
    
    // Remote logging (if enabled)
    if (this.enableRemoteLogging) {
      this.logToRemoteService(errorEntry);
    }
    
    // Store in localStorage for debugging
    this.storeErrorLocally(errorEntry);
    
    return errorEntry.id;
  }

  /**
   * Format error into standardized structure
   * @param {Error|Object} error - Error to format
   * @param {Object} context - Additional context
   * @param {string} severity - Error severity
   * @returns {Object} Formatted error object
   */
  formatError(error, context, severity) {
    const timestamp = new Date().toISOString();
    const id = this.generateErrorId();
    
    let formattedError = {
      id,
      timestamp,
      severity,
      context: {
        ...context,
        url: window.location.href,
        userAgent: navigator.userAgent,
        appVersion: APP_CONFIG.VERSION,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight,
        },
        connection: this.getConnectionInfo(),
      },
    };

    if (error instanceof Error) {
      formattedError = {
        ...formattedError,
        type: 'javascript',
        message: error.message,
        name: error.name,
        stack: error.stack,
        fileName: error.fileName,
        lineNumber: error.lineNumber,
        columnNumber: error.columnNumber,
      };
    } else if (typeof error === 'object') {
      formattedError = {
        ...formattedError,
        ...error,
      };
    } else {
      formattedError = {
        ...formattedError,
        type: 'custom',
        message: String(error),
      };
    }

    return formattedError;
  }

  /**
   * Add error to local error store
   * @param {Object} errorEntry - Formatted error entry
   */
  addToErrorStore(errorEntry) {
    this.errors.unshift(errorEntry);
    
    // Keep only the most recent errors
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(0, this.maxErrors);
    }
  }

  /**
   * Log error to console with appropriate level
   * @param {Object} errorEntry - Error entry to log
   */
  logToConsole(errorEntry) {
    const { severity, message, stack, context } = errorEntry;
    
    const logMethod = severity === 'warning' ? 'warn' : 
                     severity === 'info' ? 'info' : 'error';
    
    console.group(`🚨 ${severity.toUpperCase()}: ${message}`);
    console[logMethod]('Message:', message);
    
    if (stack) {
      console[logMethod]('Stack:', stack);
    }
    
    if (Object.keys(context).length > 0) {
      console[logMethod]('Context:', context);
    }
    
    console.groupEnd();
  }

  /**
   * Store error in localStorage for debugging
   * @param {Object} errorEntry - Error entry to store
   */
  storeErrorLocally(errorEntry) {
    try {
      const storedErrors = JSON.parse(localStorage.getItem('coinpilot_errors') || '[]');
      storedErrors.unshift(errorEntry);
      
      // Keep only last 50 errors in localStorage
      const trimmedErrors = storedErrors.slice(0, 50);
      localStorage.setItem('coinpilot_errors', JSON.stringify(trimmedErrors));
    } catch (error) {
      console.warn('Failed to store error in localStorage:', error);
    }
  }

  /**
   * Send error to remote logging service
   * @param {Object} errorEntry - Error entry to send
   */
  async logToRemoteService(errorEntry) {
    try {
      // This would integrate with services like Sentry, LogRocket, etc.
      // For now, we'll just log that it would be sent
      console.info('Would send error to remote service:', errorEntry.id);
      
      // Example implementation:
      // await fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(errorEntry),
      // });
    } catch (error) {
      console.warn('Failed to send error to remote service:', error);
    }
  }

  /**
   * Generate unique error ID
   * @returns {string} Unique error identifier
   */
  generateErrorId() {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get connection information
   * @returns {Object} Connection details
   */
  getConnectionInfo() {
    const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
    
    if (connection) {
      return {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt,
        saveData: connection.saveData,
      };
    }
    
    return { available: false };
  }

  /**
   * Get all logged errors
   * @returns {Array} Array of error entries
   */
  getErrors() {
    return [...this.errors];
  }

  /**
   * Get errors by severity
   * @param {string} severity - Severity level to filter by
   * @returns {Array} Filtered error entries
   */
  getErrorsBySeverity(severity) {
    return this.errors.filter(error => error.severity === severity);
  }

  /**
   * Clear all logged errors
   */
  clearErrors() {
    this.errors = [];
    try {
      localStorage.removeItem('coinpilot_errors');
    } catch (error) {
      console.warn('Failed to clear errors from localStorage:', error);
    }
  }

  /**
   * Export errors for debugging
   * @returns {string} JSON string of all errors
   */
  exportErrors() {
    return JSON.stringify({
      errors: this.errors,
      exportedAt: new Date().toISOString(),
      appVersion: APP_CONFIG.VERSION,
    }, null, 2);
  }

  /**
   * Log API errors with specific context
   * @param {Error} error - API error
   * @param {Object} requestContext - Request details
   */
  logAPIError(error, requestContext = {}) {
    this.logError(error, {
      type: 'api',
      endpoint: requestContext.url,
      method: requestContext.method,
      status: requestContext.status,
      responseTime: requestContext.responseTime,
    }, 'error');
  }

  /**
   * Log user action errors
   * @param {Error} error - User action error
   * @param {Object} actionContext - Action details
   */
  logUserActionError(error, actionContext = {}) {
    this.logError(error, {
      type: 'user_action',
      action: actionContext.action,
      component: actionContext.component,
      data: actionContext.data,
    }, 'error');
  }

  /**
   * Log performance issues
   * @param {string} message - Performance issue description
   * @param {Object} performanceData - Performance metrics
   */
  logPerformanceIssue(message, performanceData = {}) {
    this.logError({
      type: 'performance',
      message,
      ...performanceData,
    }, {}, 'warning');
  }
}

// Create and export singleton instance
const errorLogger = new ErrorLogger();

export default errorLogger;

// Export convenience methods
export const logError = (error, context, severity) => errorLogger.logError(error, context, severity);
export const logAPIError = (error, context) => errorLogger.logAPIError(error, context);
export const logUserActionError = (error, context) => errorLogger.logUserActionError(error, context);
export const logPerformanceIssue = (message, data) => errorLogger.logPerformanceIssue(message, data);
