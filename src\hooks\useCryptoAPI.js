import { useState, useEffect, useCallback, useRef } from 'react';
import coinGeckoAPI from '../services/coinGeckoAPI.js';
import { useCacheStorage } from './useLocalStorage.js';
import { LOADING_STATES, TIME_INTERVALS } from '../utils/constants.js';

/**
 * Custom hook for cryptocurrency API operations
 * @returns {Object} API state and methods
 */
export const useCryptoAPI = () => {
  const [loading, setLoading] = useState(LOADING_STATES.IDLE);
  const [error, setError] = useState(null);
  const [cache, setCache] = useCacheStorage();
  
  const abortControllerRef = useRef(null);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  /**
   * Check if cached data is still valid
   * @param {string} key - Cache key
   * @param {number} maxAge - Maximum age in milliseconds
   * @returns {boolean} Whether cache is valid
   */
  const isCacheValid = useCallback((key, maxAge = TIME_INTERVALS.CACHE_EXPIRY) => {
    const cached = cache[key];
    if (!cached || !cached.timestamp) return false;
    
    return Date.now() - cached.timestamp < maxAge;
  }, [cache]);

  /**
   * Get data from cache
   * @param {string} key - Cache key
   * @returns {*} Cached data or null
   */
  const getCachedData = useCallback((key) => {
    const cached = cache[key];
    return cached && isCacheValid(key) ? cached.data : null;
  }, [cache, isCacheValid]);

  /**
   * Set data in cache
   * @param {string} key - Cache key
   * @param {*} data - Data to cache
   */
  const setCachedData = useCallback((key, data) => {
    setCache(prevCache => ({
      ...prevCache,
      [key]: {
        data,
        timestamp: Date.now(),
      },
    }));
  }, [setCache]);

  /**
   * Search for cryptocurrencies
   * @param {string} query - Search query
   * @returns {Promise<Array>} Search results
   */
  const searchCoins = useCallback(async (query) => {
    if (!query || query.trim().length < 2) {
      return [];
    }

    const cacheKey = `search_${query.toLowerCase().trim()}`;
    const cachedResults = getCachedData(cacheKey);
    
    if (cachedResults) {
      return cachedResults;
    }

    try {
      setLoading(LOADING_STATES.LOADING);
      setError(null);

      // Cancel previous request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      abortControllerRef.current = new AbortController();

      const results = await coinGeckoAPI.searchCoins(query);
      
      setCachedData(cacheKey, results);
      setLoading(LOADING_STATES.SUCCESS);
      
      return results;
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Search coins error:', error);
        setError(error.message);
        setLoading(LOADING_STATES.ERROR);
      }
      return [];
    }
  }, [getCachedData, setCachedData]);

  /**
   * Get market data for multiple coins
   * @param {Array} coinIds - Array of coin IDs
   * @param {string} currency - Currency for prices
   * @returns {Promise<Array>} Market data
   */
  const getCoinsMarketData = useCallback(async (coinIds, currency = 'usd') => {
    if (!Array.isArray(coinIds) || coinIds.length === 0) {
      return [];
    }

    const cacheKey = `market_${coinIds.sort().join(',')}_${currency}`;
    const cachedData = getCachedData(cacheKey);
    
    if (cachedData) {
      return cachedData;
    }

    try {
      setLoading(LOADING_STATES.LOADING);
      setError(null);

      const data = await coinGeckoAPI.getCoinsMarketData(coinIds, currency);
      
      setCachedData(cacheKey, data);
      setLoading(LOADING_STATES.SUCCESS);
      
      return data;
    } catch (error) {
      console.error('Get market data error:', error);
      setError(error.message);
      setLoading(LOADING_STATES.ERROR);
      return [];
    }
  }, [getCachedData, setCachedData]);

  /**
   * Get simple prices for multiple coins
   * @param {Array} coinIds - Array of coin IDs
   * @param {string} currency - Currency for prices
   * @param {boolean} includeChange - Include 24h change
   * @returns {Promise<Object>} Price data
   */
  const getSimplePrices = useCallback(async (coinIds, currency = 'usd', includeChange = true) => {
    if (!Array.isArray(coinIds) || coinIds.length === 0) {
      return {};
    }

    const cacheKey = `prices_${coinIds.sort().join(',')}_${currency}_${includeChange}`;
    const cachedData = getCachedData(cacheKey);
    
    if (cachedData) {
      return cachedData;
    }

    try {
      setLoading(LOADING_STATES.LOADING);
      setError(null);

      const data = await coinGeckoAPI.getSimplePrices(coinIds, currency, includeChange);
      
      setCachedData(cacheKey, data);
      setLoading(LOADING_STATES.SUCCESS);
      
      return data;
    } catch (error) {
      console.error('Get simple prices error:', error);
      setError(error.message);
      setLoading(LOADING_STATES.ERROR);
      return {};
    }
  }, [getCachedData, setCachedData]);

  /**
   * Get detailed coin information
   * @param {string} coinId - Coin ID
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Coin details
   */
  const getCoinDetails = useCallback(async (coinId, options = {}) => {
    if (!coinId) {
      return null;
    }

    const cacheKey = `details_${coinId}_${JSON.stringify(options)}`;
    const cachedData = getCachedData(cacheKey);
    
    if (cachedData) {
      return cachedData;
    }

    try {
      setLoading(LOADING_STATES.LOADING);
      setError(null);

      const data = await coinGeckoAPI.getCoinDetails(coinId, options);
      
      setCachedData(cacheKey, data);
      setLoading(LOADING_STATES.SUCCESS);
      
      return data;
    } catch (error) {
      console.error('Get coin details error:', error);
      setError(error.message);
      setLoading(LOADING_STATES.ERROR);
      return null;
    }
  }, [getCachedData, setCachedData]);

  /**
   * Get trending cryptocurrencies
   * @returns {Promise<Array>} Trending coins
   */
  const getTrendingCoins = useCallback(async () => {
    const cacheKey = 'trending_coins';
    const cachedData = getCachedData(cacheKey);
    
    if (cachedData) {
      return cachedData;
    }

    try {
      setLoading(LOADING_STATES.LOADING);
      setError(null);

      const data = await coinGeckoAPI.getTrendingCoins();
      
      setCachedData(cacheKey, data);
      setLoading(LOADING_STATES.SUCCESS);
      
      return data;
    } catch (error) {
      console.error('Get trending coins error:', error);
      setError(error.message);
      setLoading(LOADING_STATES.ERROR);
      return [];
    }
  }, [getCachedData, setCachedData]);

  /**
   * Get global market data
   * @returns {Promise<Object>} Global market data
   */
  const getGlobalData = useCallback(async () => {
    const cacheKey = 'global_data';
    const cachedData = getCachedData(cacheKey);
    
    if (cachedData) {
      return cachedData;
    }

    try {
      setLoading(LOADING_STATES.LOADING);
      setError(null);

      const data = await coinGeckoAPI.getGlobalData();
      
      setCachedData(cacheKey, data);
      setLoading(LOADING_STATES.SUCCESS);
      
      return data;
    } catch (error) {
      console.error('Get global data error:', error);
      setError(error.message);
      setLoading(LOADING_STATES.ERROR);
      return null;
    }
  }, [getCachedData, setCachedData]);

  /**
   * Clear all cached data
   */
  const clearCache = useCallback(() => {
    setCache({});
  }, [setCache]);

  /**
   * Clear specific cached data
   * @param {string} key - Cache key to clear
   */
  const clearCachedData = useCallback((key) => {
    setCache(prevCache => {
      const newCache = { ...prevCache };
      delete newCache[key];
      return newCache;
    });
  }, [setCache]);

  /**
   * Refresh data by clearing cache and refetching
   * @param {string} key - Cache key to refresh
   * @param {Function} fetchFunction - Function to refetch data
   * @param {...*} args - Arguments for fetch function
   * @returns {Promise<*>} Refreshed data
   */
  const refreshData = useCallback(async (key, fetchFunction, ...args) => {
    clearCachedData(key);
    return await fetchFunction(...args);
  }, [clearCachedData]);

  return {
    // State
    loading,
    error,
    
    // API Methods
    searchCoins,
    getCoinsMarketData,
    getSimplePrices,
    getCoinDetails,
    getTrendingCoins,
    getGlobalData,
    
    // Cache Methods
    clearCache,
    clearCachedData,
    refreshData,
    isCacheValid,
    getCachedData,
    
    // Utilities
    isLoading: loading === LOADING_STATES.LOADING,
    hasError: loading === LOADING_STATES.ERROR,
    isSuccess: loading === LOADING_STATES.SUCCESS,
  };
};

/**
 * Hook for automatic price updates
 * @param {Array} coinIds - Coin IDs to track
 * @param {number} interval - Update interval in milliseconds
 * @param {boolean} enabled - Whether updates are enabled
 * @returns {Object} Price data and update status
 */
export const usePriceUpdates = (coinIds, interval = TIME_INTERVALS.PRICE_UPDATE, enabled = true) => {
  const [priceData, setPriceData] = useState({});
  const [lastUpdated, setLastUpdated] = useState(null);
  const { getCoinsMarketData, loading, error } = useCryptoAPI();
  
  const intervalRef = useRef(null);

  const updatePrices = useCallback(async () => {
    if (!Array.isArray(coinIds) || coinIds.length === 0) {
      return;
    }

    try {
      const data = await getCoinsMarketData(coinIds);
      const priceMap = {};
      
      data.forEach(coin => {
        priceMap[coin.id] = coin;
      });
      
      setPriceData(priceMap);
      setLastUpdated(new Date().toISOString());
    } catch (error) {
      console.error('Price update error:', error);
    }
  }, [coinIds, getCoinsMarketData]);

  // Initial fetch and setup interval
  useEffect(() => {
    if (!enabled || !Array.isArray(coinIds) || coinIds.length === 0) {
      return;
    }

    // Initial fetch
    updatePrices();

    // Setup interval
    intervalRef.current = setInterval(updatePrices, interval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [coinIds, interval, enabled, updatePrices]);

  return {
    priceData,
    lastUpdated,
    loading,
    error,
    updatePrices,
  };
};
