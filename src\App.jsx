import React, { useState, useEffect } from 'react';
import ErrorBoundary from './components/common/ErrorBoundary.jsx';
import Header from './components/common/Header.jsx';
import Footer from './components/common/Footer.jsx';
import PortfolioSummary from './components/portfolio/PortfolioSummary.jsx';
import { CoinList } from './components/portfolio/CoinCard.jsx';
import AddCoinModal from './components/portfolio/AddCoinModal.jsx';
import EditHoldingsModal from './components/portfolio/EditHoldingsModal.jsx';
import { PageLoading } from './components/common/LoadingSpinner.jsx';
import { usePortfolio } from './hooks/usePortfolio.js';
import { usePriceUpdates } from './hooks/useCryptoAPI.js';
import { APP_CONFIG, TIME_INTERVALS } from './utils/constants.js';

function App() {
  // Portfolio state
  const {
    portfolio,
    loading: portfolioLoading,
    error: portfolioError,
    message: portfolioMessage,
    lastUpdated,
    isEmpty,
    addCoin,
    updateHoldings,
    removeCoin,
    updatePrices,
    clearMessages,
    getStats,
    getPortfolioWithAllocation,
  } = usePortfolio();

  // UI state
  const [isAddCoinModalOpen, setIsAddCoinModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingCoin, setEditingCoin] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [appError, setAppError] = useState(null);

  // Get coin IDs for price updates
  const coinIds = portfolio?.map(coin => coin.id) || [];

  // Auto price updates
  const {
    priceData,
    updatePrices: updatePricesAPI,
    error: priceError
  } = usePriceUpdates(coinIds, TIME_INTERVALS.PRICE_UPDATE, !isEmpty);

  // Update portfolio prices when new price data arrives
  useEffect(() => {
    if (priceData && Object.keys(priceData).length > 0) {
      const priceArray = Object.values(priceData);
      updatePrices(priceArray);
    }
  }, [priceData, updatePrices]);

  // Handle app-level errors
  useEffect(() => {
    if (portfolioError || priceError) {
      setAppError(portfolioError || priceError);
    } else {
      setAppError(null);
    }
  }, [portfolioError, priceError]);

  // Clear messages after timeout
  useEffect(() => {
    if (portfolioMessage) {
      const timer = setTimeout(() => {
        clearMessages();
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [portfolioMessage, clearMessages]);

  // Handle manual refresh
  const handleRefresh = async () => {
    if (isEmpty) return;

    try {
      setIsRefreshing(true);
      await updatePricesAPI();
    } catch (error) {
      console.error('Refresh error:', error);
      setAppError('Failed to refresh prices. Please try again.');
    } finally {
      setIsRefreshing(false);
    }
  };

  // Handle add coin
  const handleAddCoin = async (coinData, amount) => {
    const result = await addCoin(coinData, amount);
    if (result.success) {
      setIsAddCoinModalOpen(false);
    }
    return result;
  };

  // Handle edit coin
  const handleEditCoin = (coin) => {
    setEditingCoin(coin);
    setIsEditModalOpen(true);
  };

  // Handle update holdings
  const handleUpdateHoldings = async (coinId, newAmount) => {
    const result = await updateHoldings(coinId, newAmount);
    if (result.success) {
      setIsEditModalOpen(false);
      setEditingCoin(null);
    }
    return result;
  };

  // Handle remove coin
  const handleRemoveCoin = async (coinId) => {
    const result = await removeCoin(coinId);
    if (result.success) {
      setIsEditModalOpen(false);
      setEditingCoin(null);
    }
    return result;
  };

  // Handle settings (placeholder)
  const handleSettings = () => {
    // TODO: Implement settings modal
    console.log('Settings clicked');
  };

  // Handle export (placeholder)
  const handleExport = () => {
    // TODO: Implement export functionality
    console.log('Export clicked');
  };

  // Handle import (placeholder)
  const handleImport = () => {
    // TODO: Implement import functionality
    console.log('Import clicked');
  };

  // Get portfolio stats and allocation
  const stats = getStats();
  const portfolioWithAllocation = getPortfolioWithAllocation();

  // Show loading screen on initial load
  if (portfolioLoading && isEmpty) {
    return <PageLoading message="Loading your portfolio..." />;
  }

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gray-50 flex flex-col">
        {/* Header */}
        <Header
          onAddCoin={() => setIsAddCoinModalOpen(true)}
          onRefresh={handleRefresh}
          onSettings={handleSettings}
          onExport={handleExport}
          onImport={handleImport}
          isRefreshing={isRefreshing}
          lastUpdated={lastUpdated}
        />

        {/* Main Content */}
        <main className="flex-1 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Error Message */}
          {appError && (
            <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <p className="text-sm text-red-600">{appError}</p>
                <button
                  onClick={() => setAppError(null)}
                  className="text-red-400 hover:text-red-600"
                >
                  ×
                </button>
              </div>
            </div>
          )}

          {/* Success Message */}
          {portfolioMessage && (
            <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <p className="text-sm text-green-600">{portfolioMessage}</p>
                <button
                  onClick={clearMessages}
                  className="text-green-400 hover:text-green-600"
                >
                  ×
                </button>
              </div>
            </div>
          )}

          {/* Portfolio Summary */}
          <div className="mb-8">
            <PortfolioSummary
              portfolio={portfolioWithAllocation}
              stats={stats}
              lastUpdated={lastUpdated}
              isLoading={portfolioLoading}
            />
          </div>

          {/* Portfolio Holdings */}
          {!isEmpty && (
            <div className="mb-8">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Your Holdings</h2>
                <button
                  onClick={() => setIsAddCoinModalOpen(true)}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                >
                  Add Cryptocurrency
                </button>
              </div>

              <CoinList
                coins={portfolioWithAllocation}
                onEdit={handleEditCoin}
                onRemove={(coin) => handleRemoveCoin(coin.id)}
                loading={portfolioLoading}
              />
            </div>
          )}
        </main>

        {/* Footer */}
        <Footer />

        {/* Modals */}
        <AddCoinModal
          isOpen={isAddCoinModalOpen}
          onClose={() => setIsAddCoinModalOpen(false)}
          onAddCoin={handleAddCoin}
          existingCoins={portfolio}
        />

        <EditHoldingsModal
          isOpen={isEditModalOpen}
          onClose={() => {
            setIsEditModalOpen(false);
            setEditingCoin(null);
          }}
          onUpdateHoldings={handleUpdateHoldings}
          onRemoveCoin={handleRemoveCoin}
          coin={editingCoin}
        />
      </div>
    </ErrorBoundary>
  );
}

export default App;
