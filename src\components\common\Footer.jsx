import React from 'react';
import { Heart, Github, ExternalLink } from 'lucide-react';
import { APP_CONFIG } from '../../utils/constants.js';

/**
 * Footer Component
 * App footer with links and attribution
 */
const Footer = ({ className = "" }) => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className={`footer bg-gray-50 border-t border-gray-200 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Brand Section */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded flex items-center justify-center">
                <span className="text-white font-bold text-xs">CP</span>
              </div>
              <span className="font-semibold text-gray-900">{APP_CONFIG.NAME}</span>
            </div>
            <p className="text-sm text-gray-600 max-w-md">
              Professional navigation for your cryptocurrency investments. 
              Track, analyze, and visualize your crypto portfolio with real-time data.
            </p>
            <div className="flex items-center space-x-1 text-sm text-gray-500">
              <span>Made with</span>
              <Heart className="w-4 h-4 text-red-500 fill-current" />
              <span>by</span>
              <a 
                href="https://github.com/HectorTa1989" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                HectorTa1989
              </a>
            </div>
          </div>

          {/* Links Section */}
          <div className="space-y-4">
            <h3 className="font-semibold text-gray-900">Resources</h3>
            <div className="space-y-2">
              <a
                href="https://github.com/HectorTa1989/coinpilot"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 text-sm text-gray-600 hover:text-gray-900 transition-colors"
              >
                <Github className="w-4 h-4" />
                <span>GitHub Repository</span>
                <ExternalLink className="w-3 h-3" />
              </a>
              <a
                href="https://coingecko.com"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 text-sm text-gray-600 hover:text-gray-900 transition-colors"
              >
                <span>🦎</span>
                <span>Powered by CoinGecko</span>
                <ExternalLink className="w-3 h-3" />
              </a>
              <a
                href="https://reactjs.org"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 text-sm text-gray-600 hover:text-gray-900 transition-colors"
              >
                <span>⚛️</span>
                <span>Built with React</span>
                <ExternalLink className="w-3 h-3" />
              </a>
            </div>
          </div>

          {/* Info Section */}
          <div className="space-y-4">
            <h3 className="font-semibold text-gray-900">Information</h3>
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex justify-between">
                <span>Version:</span>
                <span className="font-mono">{APP_CONFIG.VERSION}</span>
              </div>
              <div className="flex justify-between">
                <span>Data Source:</span>
                <span>CoinGecko API</span>
              </div>
              <div className="flex justify-between">
                <span>Storage:</span>
                <span>Local Browser</span>
              </div>
              <div className="flex justify-between">
                <span>Updates:</span>
                <span>Real-time</span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-8 pt-8 border-t border-gray-200">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-sm text-gray-500">
              © {currentYear} {APP_CONFIG.NAME}. All rights reserved.
            </div>
            
            <div className="flex items-center space-x-6 text-sm text-gray-500">
              <span>Free & Open Source</span>
              <span>•</span>
              <span>No Registration Required</span>
              <span>•</span>
              <span>Privacy First</span>
            </div>
          </div>

          {/* Disclaimer */}
          <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-xs text-yellow-800">
              <strong>Disclaimer:</strong> This application is for informational purposes only and should not be considered as financial advice. 
              Cryptocurrency investments are subject to market risks. Always do your own research before making investment decisions.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
