import React, { useMemo } from 'react';
import { Doughn<PERSON> } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  Title,
} from 'chart.js';
import { CHART_CONFIG } from '../../utils/constants.js';
import { formatCurrency, formatPercentage } from '../../utils/formatters.js';

// Register Chart.js components
ChartJS.register(ArcElement, Tooltip, Legend, Title);

/**
 * Portfolio Allocation Chart Component
 * Displays portfolio allocation as a doughnut chart
 */
const AllocationChart = ({ 
  portfolio = [], 
  title = "Portfolio Allocation",
  showLegend = true,
  showTooltips = true,
  height = 400,
  className = "",
}) => {
  // Prepare chart data
  const chartData = useMemo(() => {
    if (!Array.isArray(portfolio) || portfolio.length === 0) {
      return {
        labels: ['No Data'],
        datasets: [{
          data: [1],
          backgroundColor: ['#e5e7eb'],
          borderColor: ['#d1d5db'],
          borderWidth: 1,
        }],
      };
    }

    // Filter out items with zero allocation and sort by allocation
    const validItems = portfolio
      .filter(item => item.allocation > 0)
      .sort((a, b) => b.allocation - a.allocation);

    if (validItems.length === 0) {
      return {
        labels: ['No Data'],
        datasets: [{
          data: [1],
          backgroundColor: ['#e5e7eb'],
          borderColor: ['#d1d5db'],
          borderWidth: 1,
        }],
      };
    }

    const labels = validItems.map(item => 
      `${item.name} (${item.symbol.toUpperCase()})`
    );

    const data = validItems.map(item => item.allocation);

    const backgroundColor = validItems.map((_, index) => 
      CHART_CONFIG.COLORS[index % CHART_CONFIG.COLORS.length]
    );

    const borderColor = backgroundColor.map(color => color);

    return {
      labels,
      datasets: [{
        data,
        backgroundColor,
        borderColor,
        borderWidth: 2,
        hoverBorderWidth: 3,
        hoverOffset: 10,
      }],
    };
  }, [portfolio]);

  // Chart options
  const options = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      title: {
        display: !!title,
        text: title,
        font: {
          size: 16,
          weight: 'bold',
        },
        padding: {
          top: 10,
          bottom: 20,
        },
      },
      legend: {
        display: showLegend,
        position: 'bottom',
        labels: {
          padding: 20,
          usePointStyle: true,
          font: {
            size: 12,
          },
          generateLabels: (chart) => {
            const data = chart.data;
            if (data.labels.length && data.datasets.length) {
              return data.labels.map((label, index) => {
                const dataset = data.datasets[0];
                const value = dataset.data[index];
                const backgroundColor = dataset.backgroundColor[index];
                
                return {
                  text: `${label}: ${formatPercentage(value)}`,
                  fillStyle: backgroundColor,
                  strokeStyle: backgroundColor,
                  lineWidth: 0,
                  pointStyle: 'circle',
                  hidden: false,
                  index,
                };
              });
            }
            return [];
          },
        },
      },
      tooltip: {
        enabled: showTooltips,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: '#374151',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          title: (context) => {
            const index = context[0].dataIndex;
            const item = portfolio.find((_, i) => i === index);
            return item ? `${item.name} (${item.symbol.toUpperCase()})` : '';
          },
          label: (context) => {
            const index = context.dataIndex;
            const item = portfolio[index];
            if (!item) return '';

            const lines = [
              `Allocation: ${formatPercentage(item.allocation)}`,
              `Value: ${formatCurrency(item.value)}`,
              `Amount: ${item.amount.toLocaleString()} ${item.symbol.toUpperCase()}`,
              `Price: ${formatCurrency(item.currentPrice)}`,
            ];

            if (item.priceChange24h !== undefined) {
              const changeColor = item.priceChange24h >= 0 ? '🟢' : '🔴';
              lines.push(`24h Change: ${changeColor} ${formatPercentage(item.priceChange24h)}`);
            }

            return lines;
          },
        },
      },
    },
    animation: {
      animateRotate: true,
      animateScale: true,
      duration: CHART_CONFIG.ANIMATION_DURATION,
    },
    cutout: '60%', // Creates doughnut effect
    radius: '90%',
    interaction: {
      intersect: false,
      mode: 'index',
    },
  }), [portfolio, title, showLegend, showTooltips]);

  // Handle empty portfolio
  if (!Array.isArray(portfolio) || portfolio.length === 0) {
    return (
      <div className={`allocation-chart-container ${className}`}>
        <div className="empty-chart" style={{ height }}>
          <div className="empty-chart-content">
            <div className="empty-chart-icon">📊</div>
            <h3>No Portfolio Data</h3>
            <p>Add some cryptocurrencies to see your portfolio allocation</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`allocation-chart-container ${className}`}>
      <div className="chart-wrapper" style={{ height }}>
        <Doughnut data={chartData} options={options} />
      </div>
      
      {/* Additional portfolio stats */}
      <div className="chart-stats">
        <div className="stats-grid">
          <div className="stat-item">
            <span className="stat-label">Total Assets</span>
            <span className="stat-value">{portfolio.length}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Largest Holding</span>
            <span className="stat-value">
              {portfolio.length > 0 
                ? `${formatPercentage(Math.max(...portfolio.map(p => p.allocation)))}`
                : '0%'
              }
            </span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Smallest Holding</span>
            <span className="stat-value">
              {portfolio.length > 0 
                ? `${formatPercentage(Math.min(...portfolio.map(p => p.allocation)))}`
                : '0%'
              }
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AllocationChart;
