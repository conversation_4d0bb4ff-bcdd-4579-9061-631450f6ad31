# CoinPilot Environment Configuration
# Copy this file to .env and update the values as needed

# Application Configuration
VITE_APP_NAME=CoinPilot
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION="Professional navigation for your cryptocurrency investments"

# API Configuration
VITE_COINGECKO_API_URL=https://api.coingecko.com/api/v3
VITE_API_TIMEOUT=10000
VITE_API_RATE_LIMIT_DELAY=2000
VITE_API_MAX_REQUESTS_PER_MINUTE=30

# Feature Flags
VITE_ENABLE_SERVICE_WORKER=true
VITE_ENABLE_NOTIFICATIONS=false
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_ERROR_REPORTING=false

# Development Configuration
VITE_DEV_MODE=false
VITE_DEBUG_MODE=false
VITE_MOCK_API=false

# Cache Configuration
VITE_CACHE_EXPIRY_TIME=300000
VITE_PRICE_UPDATE_INTERVAL=60000

# UI Configuration
VITE_DEFAULT_CURRENCY=usd
VITE_DEFAULT_THEME=light
VITE_MAX_PORTFOLIO_SIZE=50

# External Services (Optional)
# VITE_GOOGLE_ANALYTICS_ID=
# VITE_SENTRY_DSN=
# VITE_HOTJAR_ID=

# Social Media (for sharing)
VITE_TWITTER_HANDLE=@HectorTa1989
VITE_GITHUB_USERNAME=HectorTa1989
VITE_GITHUB_REPO=coinpilot

# Deployment Configuration
VITE_BASE_URL=/coinpilot/
VITE_PUBLIC_URL=https://hectortai1989.github.io/coinpilot/

# Security Configuration
VITE_CSP_ENABLED=true
VITE_HTTPS_ONLY=true
