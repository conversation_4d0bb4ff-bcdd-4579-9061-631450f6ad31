import React, { useState, useEffect, useRef } from 'react';
import { X, Save, Trash2 } from 'lucide-react';
import { formatCurrency, formatCryptoAmount } from '../../utils/formatters.js';
import { calculateItemValue } from '../../utils/calculations.js';
import LoadingSpinner from '../common/LoadingSpinner.jsx';

/**
 * Edit Holdings Modal Component
 * Modal for editing cryptocurrency holdings amount
 */
const EditHoldingsModal = ({
  isOpen,
  onClose,
  onUpdateHoldings,
  onRemoveCoin,
  coin,
  className = "",
}) => {
  const [amount, setAmount] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);
  const [isRemoving, setIsRemoving] = useState(false);
  const [error, setError] = useState('');

  const amountInputRef = useRef(null);

  // Initialize amount when modal opens
  useEffect(() => {
    if (isOpen && coin) {
      setAmount(coin.amount?.toString() || '');
      setError('');
      
      // Focus input after a short delay
      setTimeout(() => {
        if (amountInputRef.current) {
          amountInputRef.current.focus();
          amountInputRef.current.select();
        }
      }, 100);
    }
  }, [isOpen, coin]);

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!coin || !amount) {
      setError('Please enter a valid amount.');
      return;
    }

    const amountNum = parseFloat(amount);
    if (isNaN(amountNum) || amountNum < 0) {
      setError('Please enter a valid amount greater than or equal to 0.');
      return;
    }

    if (amountNum === 0) {
      handleRemove();
      return;
    }

    try {
      setIsUpdating(true);
      setError('');
      
      const result = await onUpdateHoldings(coin.id, amountNum);
      
      if (result.success) {
        handleClose();
      } else {
        setError(result.message || 'Failed to update holdings.');
      }
    } catch (error) {
      console.error('Error updating holdings:', error);
      setError('Failed to update holdings. Please try again.');
    } finally {
      setIsUpdating(false);
    }
  };

  // Handle remove coin
  const handleRemove = async () => {
    if (!coin) return;

    try {
      setIsRemoving(true);
      setError('');
      
      const result = await onRemoveCoin(coin.id);
      
      if (result.success) {
        handleClose();
      } else {
        setError(result.message || 'Failed to remove cryptocurrency.');
      }
    } catch (error) {
      console.error('Error removing coin:', error);
      setError('Failed to remove cryptocurrency. Please try again.');
    } finally {
      setIsRemoving(false);
    }
  };

  // Handle modal close
  const handleClose = () => {
    setAmount('');
    setError('');
    setIsUpdating(false);
    setIsRemoving(false);
    onClose();
  };

  // Handle keyboard events
  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      handleClose();
    }
  };

  if (!isOpen || !coin) return null;

  const currentValue = calculateItemValue(coin.amount, coin.currentPrice);
  const newAmount = parseFloat(amount) || 0;
  const newValue = calculateItemValue(newAmount, coin.currentPrice);
  const valueDifference = newValue - currentValue;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div 
        className={`bg-white rounded-lg shadow-xl max-w-md w-full ${className}`}
        onKeyDown={handleKeyDown}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Edit Holdings</h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Coin Info */}
            <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg">
              {coin.image && (
                <img
                  src={coin.image}
                  alt={coin.name}
                  className="w-12 h-12 rounded-full"
                  onError={(e) => {
                    e.target.style.display = 'none';
                  }}
                />
              )}
              <div>
                <h3 className="font-semibold text-gray-900">{coin.name}</h3>
                <p className="text-sm text-gray-500 uppercase">{coin.symbol}</p>
                <p className="text-sm text-gray-600">
                  Current price: {formatCurrency(coin.currentPrice)}
                </p>
              </div>
            </div>

            {/* Current Holdings */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">Current Holdings</h4>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-blue-700">Amount:</span>
                  <span className="font-medium text-blue-900">
                    {formatCryptoAmount(coin.amount, coin.symbol)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-blue-700">Value:</span>
                  <span className="font-medium text-blue-900">
                    {formatCurrency(currentValue)}
                  </span>
                </div>
              </div>
            </div>

            {/* Amount Input */}
            <div>
              <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-2">
                New Amount of {coin.symbol?.toUpperCase()}
              </label>
              <input
                id="amount"
                ref={amountInputRef}
                type="number"
                step="any"
                min="0"
                placeholder="0.00"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
              <p className="mt-1 text-xs text-gray-500">
                Enter 0 to remove this cryptocurrency from your portfolio
              </p>
            </div>

            {/* Value Preview */}
            {amount && !isNaN(parseFloat(amount)) && (
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">Preview</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">New amount:</span>
                    <span className="font-medium text-gray-900">
                      {formatCryptoAmount(newAmount, coin.symbol)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">New value:</span>
                    <span className="font-medium text-gray-900">
                      {formatCurrency(newValue)}
                    </span>
                  </div>
                  <div className="flex justify-between pt-2 border-t border-gray-200">
                    <span className="text-gray-600">Difference:</span>
                    <span className={`font-medium ${
                      valueDifference >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {valueDifference >= 0 ? '+' : ''}{formatCurrency(valueDifference)}
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            {/* Actions */}
            <div className="flex space-x-3">
              <button
                type="button"
                onClick={handleRemove}
                disabled={isRemoving || isUpdating}
                className="flex items-center justify-center px-4 py-2 border border-red-300 text-red-700 rounded-lg hover:bg-red-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isRemoving ? (
                  <LoadingSpinner size="small" color="red" />
                ) : (
                  <>
                    <Trash2 className="w-4 h-4 mr-2" />
                    Remove
                  </>
                )}
              </button>
              
              <button
                type="submit"
                disabled={isUpdating || isRemoving || !amount}
                className="flex-1 flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isUpdating ? (
                  <LoadingSpinner size="small" color="white" />
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Update Holdings
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default EditHoldingsModal;
