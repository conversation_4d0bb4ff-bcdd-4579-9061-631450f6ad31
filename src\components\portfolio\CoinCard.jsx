import React, { useState } from 'react';
import { Edit2, Trash2, TrendingUp, TrendingDown, MoreVertical } from 'lucide-react';
import { formatCurrency, formatPercentage, formatCryptoAmount } from '../../utils/formatters.js';
import { calculateItemValue } from '../../utils/calculations.js';

/**
 * Coin Card Component
 * Displays individual cryptocurrency holding information
 */
const CoinCard = ({
  coin,
  onEdit,
  onRemove,
  className = "",
  compact = false,
}) => {
  const [showActions, setShowActions] = useState(false);

  if (!coin) {
    return null;
  }

  const {
    id,
    name,
    symbol,
    image,
    amount,
    currentPrice,
    priceChange24h = 0,
    allocation = 0,
  } = coin;

  const totalValue = calculateItemValue(amount, currentPrice);
  const isPositiveChange = priceChange24h >= 0;

  const handleEdit = () => {
    setShowActions(false);
    onEdit?.(coin);
  };

  const handleRemove = () => {
    setShowActions(false);
    onRemove?.(coin);
  };

  // Compact version for mobile/small screens
  if (compact) {
    return (
      <div className={`coin-card-compact bg-white border border-gray-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 flex-1">
            {/* Coin Image */}
            <div className="w-8 h-8 flex-shrink-0">
              {image ? (
                <img
                  src={image}
                  alt={name}
                  className="w-full h-full rounded-full"
                  onError={(e) => {
                    e.target.style.display = 'none';
                  }}
                />
              ) : (
                <div className="w-full h-full bg-gray-200 rounded-full flex items-center justify-center">
                  <span className="text-xs font-medium text-gray-600">
                    {symbol?.charAt(0)?.toUpperCase()}
                  </span>
                </div>
              )}
            </div>

            {/* Coin Info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2">
                <h3 className="font-medium text-gray-900 truncate">{name}</h3>
                <span className="text-sm text-gray-500 uppercase">{symbol}</span>
              </div>
              <div className="flex items-center space-x-2 mt-1">
                <span className="text-sm font-medium text-gray-900">
                  {formatCurrency(totalValue)}
                </span>
                <span className={`text-xs font-medium ${
                  isPositiveChange ? 'text-green-600' : 'text-red-600'
                }`}>
                  {isPositiveChange ? '+' : ''}{formatPercentage(priceChange24h)}
                </span>
              </div>
            </div>

            {/* Actions */}
            <div className="relative">
              <button
                onClick={() => setShowActions(!showActions)}
                className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <MoreVertical className="w-4 h-4" />
              </button>

              {showActions && (
                <div className="absolute right-0 top-8 bg-white border border-gray-200 rounded-lg shadow-lg py-1 z-10">
                  <button
                    onClick={handleEdit}
                    className="flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >
                    <Edit2 className="w-4 h-4 mr-2" />
                    Edit
                  </button>
                  <button
                    onClick={handleRemove}
                    className="flex items-center w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Remove
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Full version for desktop
  return (
    <div className={`coin-card bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow ${className}`}>
      <div className="flex items-start justify-between mb-4">
        {/* Coin Header */}
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 flex-shrink-0">
            {image ? (
              <img
                src={image}
                alt={name}
                className="w-full h-full rounded-full"
                onError={(e) => {
                  e.target.style.display = 'none';
                }}
              />
            ) : (
              <div className="w-full h-full bg-gray-200 rounded-full flex items-center justify-center">
                <span className="text-lg font-medium text-gray-600">
                  {symbol?.charAt(0)?.toUpperCase()}
                </span>
              </div>
            )}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{name}</h3>
            <p className="text-sm text-gray-500 uppercase">{symbol}</p>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-2">
          <button
            onClick={handleEdit}
            className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
            title="Edit holdings"
          >
            <Edit2 className="w-4 h-4" />
          </button>
          <button
            onClick={handleRemove}
            className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
            title="Remove from portfolio"
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Holdings Info */}
      <div className="space-y-3">
        {/* Amount and Value */}
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">Holdings</span>
          <span className="font-medium text-gray-900">
            {formatCryptoAmount(amount, symbol)}
          </span>
        </div>

        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">Current Price</span>
          <span className="font-medium text-gray-900">
            {formatCurrency(currentPrice)}
          </span>
        </div>

        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">Total Value</span>
          <span className="text-lg font-bold text-gray-900">
            {formatCurrency(totalValue)}
          </span>
        </div>

        {/* Allocation */}
        {allocation > 0 && (
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Portfolio Allocation</span>
            <span className="font-medium text-gray-900">
              {formatPercentage(allocation)}
            </span>
          </div>
        )}
      </div>

      {/* Price Change */}
      <div className="mt-4 pt-4 border-t border-gray-100">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">24h Change</span>
          <div className={`flex items-center space-x-1 ${
            isPositiveChange ? 'text-green-600' : 'text-red-600'
          }`}>
            {isPositiveChange ? (
              <TrendingUp className="w-4 h-4" />
            ) : (
              <TrendingDown className="w-4 h-4" />
            )}
            <span className="font-medium">
              {isPositiveChange ? '+' : ''}{formatPercentage(priceChange24h)}
            </span>
          </div>
        </div>

        {/* Progress Bar for Allocation */}
        {allocation > 0 && (
          <div className="mt-3">
            <div className="flex justify-between text-xs text-gray-500 mb-1">
              <span>Portfolio Weight</span>
              <span>{formatPercentage(allocation)}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(allocation, 100)}%` }}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * Coin List Component
 * Displays a list of coin cards with responsive layout
 */
export const CoinList = ({
  coins = [],
  onEdit,
  onRemove,
  loading = false,
  className = "",
}) => {
  if (loading) {
    return (
      <div className={`coin-list ${className}`}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map(i => (
            <div key={i} className="bg-white border border-gray-200 rounded-lg p-6 animate-pulse">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gray-300 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                </div>
              </div>
              <div className="space-y-3">
                <div className="h-3 bg-gray-300 rounded"></div>
                <div className="h-3 bg-gray-300 rounded w-5/6"></div>
                <div className="h-4 bg-gray-300 rounded w-2/3"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!Array.isArray(coins) || coins.length === 0) {
    return (
      <div className={`coin-list ${className}`}>
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-2xl">💰</span>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No cryptocurrencies yet</h3>
          <p className="text-gray-600">Add your first cryptocurrency to start tracking your portfolio.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`coin-list ${className}`}>
      {/* Desktop Grid */}
      <div className="hidden md:grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {coins.map(coin => (
          <CoinCard
            key={coin.id}
            coin={coin}
            onEdit={onEdit}
            onRemove={onRemove}
          />
        ))}
      </div>

      {/* Mobile List */}
      <div className="md:hidden space-y-3">
        {coins.map(coin => (
          <CoinCard
            key={coin.id}
            coin={coin}
            onEdit={onEdit}
            onRemove={onRemove}
            compact={true}
          />
        ))}
      </div>
    </div>
  );
};

export default CoinCard;
