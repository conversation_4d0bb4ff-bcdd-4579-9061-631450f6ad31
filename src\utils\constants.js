// API Configuration
export const API_CONFIG = {
  COINGECKO_BASE_URL: import.meta.env.VITE_COINGECKO_API_URL || 'https://api.coingecko.com/api/v3',
  REQUEST_TIMEOUT: 10000,
  RATE_LIMIT_DELAY: 2000, // 2 seconds between requests
};

// App Configuration
export const APP_CONFIG = {
  NAME: import.meta.env.VITE_APP_NAME || 'CoinPilot',
  VERSION: import.meta.env.VITE_APP_VERSION || '1.0.0',
  STORAGE_KEY: 'coinpilot_portfolio',
  MAX_PORTFOLIO_SIZE: 50,
  PRICE_UPDATE_INTERVAL: 60000, // 1 minute
};

// Currency Configuration
export const CURRENCY_CONFIG = {
  DEFAULT: 'usd',
  SYMBOL: '$',
  LOCALE: 'en-US',
};

// Chart Configuration
export const CHART_CONFIG = {
  COLORS: [
    '#FF6384',
    '#36A2EB',
    '#FFCE56',
    '#4BC0C0',
    '#9966FF',
    '#FF9F40',
    '#FF6384',
    '#C9CBCF',
    '#4BC0C0',
    '#FF6384',
  ],
  ANIMATION_DURATION: 1000,
};

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection and try again.',
  API_ERROR: 'Unable to fetch cryptocurrency data. Please try again later.',
  STORAGE_ERROR: 'Unable to save portfolio data. Please check your browser settings.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  COIN_NOT_FOUND: 'Cryptocurrency not found. Please try a different search term.',
  DUPLICATE_COIN: 'This cryptocurrency is already in your portfolio.',
  PORTFOLIO_FULL: `Portfolio is full. Maximum ${APP_CONFIG.MAX_PORTFOLIO_SIZE} cryptocurrencies allowed.`,
};

// Success Messages
export const SUCCESS_MESSAGES = {
  COIN_ADDED: 'Cryptocurrency added to portfolio successfully!',
  COIN_UPDATED: 'Holdings updated successfully!',
  COIN_REMOVED: 'Cryptocurrency removed from portfolio.',
  PORTFOLIO_SAVED: 'Portfolio saved successfully!',
};

// Loading States
export const LOADING_STATES = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
};

// Local Storage Keys
export const STORAGE_KEYS = {
  PORTFOLIO: 'coinpilot_portfolio',
  SETTINGS: 'coinpilot_settings',
  CACHE: 'coinpilot_cache',
};

// API Endpoints
export const API_ENDPOINTS = {
  COINS_LIST: '/coins/list',
  COINS_MARKETS: '/coins/markets',
  COIN_DETAIL: '/coins',
  SEARCH: '/search',
  SIMPLE_PRICE: '/simple/price',
};

// Validation Rules
export const VALIDATION_RULES = {
  MIN_HOLDING_AMOUNT: 0.00000001,
  MAX_HOLDING_AMOUNT: 999999999,
  MAX_COIN_NAME_LENGTH: 100,
  MAX_SEARCH_RESULTS: 20,
};

// Time Intervals
export const TIME_INTERVALS = {
  PRICE_UPDATE: 60000, // 1 minute
  CACHE_EXPIRY: 300000, // 5 minutes
  RETRY_DELAY: 3000, // 3 seconds
};

// Responsive Breakpoints
export const BREAKPOINTS = {
  MOBILE: 768,
  TABLET: 1024,
  DESKTOP: 1200,
};

// Default Portfolio Item
export const DEFAULT_PORTFOLIO_ITEM = {
  id: '',
  symbol: '',
  name: '',
  amount: 0,
  currentPrice: 0,
  priceChange24h: 0,
  image: '',
  addedAt: new Date().toISOString(),
};

// Chart Types
export const CHART_TYPES = {
  PIE: 'pie',
  DOUGHNUT: 'doughnut',
  LINE: 'line',
  BAR: 'bar',
};

// Sort Options
export const SORT_OPTIONS = {
  NAME_ASC: 'name_asc',
  NAME_DESC: 'name_desc',
  VALUE_ASC: 'value_asc',
  VALUE_DESC: 'value_desc',
  CHANGE_ASC: 'change_asc',
  CHANGE_DESC: 'change_desc',
  AMOUNT_ASC: 'amount_asc',
  AMOUNT_DESC: 'amount_desc',
};
