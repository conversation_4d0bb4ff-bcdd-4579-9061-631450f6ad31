import {
  calculateTotalPortfolioValue,
  calculatePortfolioAllocation,
  calculatePortfolioChange,
  calculatePortfolioStats,
  calculateDiversificationScore,
  calculateItemValue,
} from '../utils/calculations.js';
import { DEFAULT_PORTFOLIO_ITEM, APP_CONFIG, ERROR_MESSAGES } from '../utils/constants.js';

/**
 * Portfolio Service - Handles all portfolio-related operations
 */
class PortfolioService {
  constructor() {
    this.portfolio = [];
    this.listeners = [];
  }

  /**
   * Add a listener for portfolio changes
   * @param {Function} listener - Callback function
   */
  addListener(listener) {
    this.listeners.push(listener);
  }

  /**
   * Remove a listener
   * @param {Function} listener - Callback function to remove
   */
  removeListener(listener) {
    this.listeners = this.listeners.filter(l => l !== listener);
  }

  /**
   * Notify all listeners of portfolio changes
   */
  notifyListeners() {
    this.listeners.forEach(listener => listener(this.portfolio));
  }

  /**
   * Set the entire portfolio
   * @param {Array} portfolio - New portfolio array
   */
  setPortfolio(portfolio) {
    if (!Array.isArray(portfolio)) {
      throw new Error('Portfolio must be an array');
    }
    this.portfolio = portfolio;
    this.notifyListeners();
  }

  /**
   * Get the current portfolio
   * @returns {Array} Current portfolio
   */
  getPortfolio() {
    return [...this.portfolio];
  }

  /**
   * Add a cryptocurrency to the portfolio
   * @param {Object} coinData - Cryptocurrency data
   * @param {number} amount - Amount to add
   * @returns {Object} Result object with success status and message
   */
  addCoin(coinData, amount) {
    try {
      // Validate inputs
      if (!coinData || !coinData.id) {
        return { success: false, message: ERROR_MESSAGES.VALIDATION_ERROR };
      }

      if (!amount || amount <= 0 || isNaN(amount)) {
        return { success: false, message: 'Please enter a valid amount' };
      }

      // Check portfolio size limit
      if (this.portfolio.length >= APP_CONFIG.MAX_PORTFOLIO_SIZE) {
        return { success: false, message: ERROR_MESSAGES.PORTFOLIO_FULL };
      }

      // Check if coin already exists
      const existingCoin = this.portfolio.find(item => item.id === coinData.id);
      if (existingCoin) {
        return { success: false, message: ERROR_MESSAGES.DUPLICATE_COIN };
      }

      // Create new portfolio item
      const newItem = {
        ...DEFAULT_PORTFOLIO_ITEM,
        id: coinData.id,
        symbol: coinData.symbol?.toLowerCase() || '',
        name: coinData.name || '',
        amount: parseFloat(amount),
        currentPrice: coinData.current_price || 0,
        priceChange24h: coinData.price_change_percentage_24h || 0,
        image: coinData.image || '',
        addedAt: new Date().toISOString(),
      };

      this.portfolio.push(newItem);
      this.notifyListeners();

      return { success: true, message: 'Cryptocurrency added successfully!' };
    } catch (error) {
      console.error('Error adding coin to portfolio:', error);
      return { success: false, message: ERROR_MESSAGES.VALIDATION_ERROR };
    }
  }

  /**
   * Update holdings for an existing cryptocurrency
   * @param {string} coinId - Cryptocurrency ID
   * @param {number} newAmount - New amount
   * @returns {Object} Result object with success status and message
   */
  updateHoldings(coinId, newAmount) {
    try {
      if (!coinId || (!newAmount && newAmount !== 0) || isNaN(newAmount)) {
        return { success: false, message: ERROR_MESSAGES.VALIDATION_ERROR };
      }

      const coinIndex = this.portfolio.findIndex(item => item.id === coinId);
      if (coinIndex === -1) {
        return { success: false, message: 'Cryptocurrency not found in portfolio' };
      }

      if (newAmount <= 0) {
        // Remove the coin if amount is 0 or negative
        return this.removeCoin(coinId);
      }

      this.portfolio[coinIndex].amount = parseFloat(newAmount);
      this.notifyListeners();

      return { success: true, message: 'Holdings updated successfully!' };
    } catch (error) {
      console.error('Error updating holdings:', error);
      return { success: false, message: ERROR_MESSAGES.VALIDATION_ERROR };
    }
  }

  /**
   * Remove a cryptocurrency from the portfolio
   * @param {string} coinId - Cryptocurrency ID
   * @returns {Object} Result object with success status and message
   */
  removeCoin(coinId) {
    try {
      if (!coinId) {
        return { success: false, message: ERROR_MESSAGES.VALIDATION_ERROR };
      }

      const initialLength = this.portfolio.length;
      this.portfolio = this.portfolio.filter(item => item.id !== coinId);

      if (this.portfolio.length === initialLength) {
        return { success: false, message: 'Cryptocurrency not found in portfolio' };
      }

      this.notifyListeners();
      return { success: true, message: 'Cryptocurrency removed from portfolio' };
    } catch (error) {
      console.error('Error removing coin from portfolio:', error);
      return { success: false, message: ERROR_MESSAGES.VALIDATION_ERROR };
    }
  }

  /**
   * Update prices for all cryptocurrencies in the portfolio
   * @param {Array} priceData - Array of price data from API
   * @returns {Object} Result object with success status and updated count
   */
  updatePrices(priceData) {
    try {
      if (!Array.isArray(priceData)) {
        return { success: false, message: 'Invalid price data' };
      }

      let updatedCount = 0;

      this.portfolio.forEach(item => {
        const priceInfo = priceData.find(price => price.id === item.id);
        if (priceInfo) {
          item.currentPrice = priceInfo.current_price || item.currentPrice;
          item.priceChange24h = priceInfo.price_change_percentage_24h || item.priceChange24h;
          item.image = priceInfo.image || item.image;
          updatedCount++;
        }
      });

      if (updatedCount > 0) {
        this.notifyListeners();
      }

      return { 
        success: true, 
        message: `Updated prices for ${updatedCount} cryptocurrencies`,
        updatedCount 
      };
    } catch (error) {
      console.error('Error updating prices:', error);
      return { success: false, message: 'Failed to update prices' };
    }
  }

  /**
   * Get portfolio statistics
   * @returns {Object} Portfolio statistics
   */
  getPortfolioStats() {
    return calculatePortfolioStats(this.portfolio);
  }

  /**
   * Get portfolio with allocation data
   * @returns {Array} Portfolio with allocation percentages
   */
  getPortfolioWithAllocation() {
    return calculatePortfolioAllocation(this.portfolio);
  }

  /**
   * Get total portfolio value
   * @returns {number} Total portfolio value
   */
  getTotalValue() {
    return calculateTotalPortfolioValue(this.portfolio);
  }

  /**
   * Get portfolio change data
   * @returns {Object} Portfolio change information
   */
  getPortfolioChange() {
    return calculatePortfolioChange(this.portfolio);
  }

  /**
   * Get diversification score
   * @returns {number} Diversification score (0-100)
   */
  getDiversificationScore() {
    return calculateDiversificationScore(this.portfolio);
  }

  /**
   * Clear the entire portfolio
   * @returns {Object} Result object with success status
   */
  clearPortfolio() {
    try {
      this.portfolio = [];
      this.notifyListeners();
      return { success: true, message: 'Portfolio cleared successfully' };
    } catch (error) {
      console.error('Error clearing portfolio:', error);
      return { success: false, message: 'Failed to clear portfolio' };
    }
  }

  /**
   * Get portfolio item by ID
   * @param {string} coinId - Cryptocurrency ID
   * @returns {Object|null} Portfolio item or null if not found
   */
  getPortfolioItem(coinId) {
    return this.portfolio.find(item => item.id === coinId) || null;
  }

  /**
   * Check if a cryptocurrency is in the portfolio
   * @param {string} coinId - Cryptocurrency ID
   * @returns {boolean} Whether the coin is in the portfolio
   */
  hasCoin(coinId) {
    return this.portfolio.some(item => item.id === coinId);
  }

  /**
   * Get portfolio sorted by specified criteria
   * @param {string} sortBy - Sort criteria
   * @param {string} order - Sort order ('asc' or 'desc')
   * @returns {Array} Sorted portfolio
   */
  getSortedPortfolio(sortBy = 'value', order = 'desc') {
    const portfolioWithAllocation = this.getPortfolioWithAllocation();
    
    return portfolioWithAllocation.sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'symbol':
          aValue = a.symbol.toLowerCase();
          bValue = b.symbol.toLowerCase();
          break;
        case 'value':
          aValue = calculateItemValue(a.amount, a.currentPrice);
          bValue = calculateItemValue(b.amount, b.currentPrice);
          break;
        case 'amount':
          aValue = a.amount;
          bValue = b.amount;
          break;
        case 'price':
          aValue = a.currentPrice;
          bValue = b.currentPrice;
          break;
        case 'change':
          aValue = a.priceChange24h;
          bValue = b.priceChange24h;
          break;
        case 'allocation':
          aValue = a.allocation;
          bValue = b.allocation;
          break;
        default:
          aValue = calculateItemValue(a.amount, a.currentPrice);
          bValue = calculateItemValue(b.amount, b.currentPrice);
      }

      if (order === 'asc') {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
      } else {
        return aValue < bValue ? 1 : aValue > bValue ? -1 : 0;
      }
    });
  }

  /**
   * Export portfolio data
   * @returns {Object} Exportable portfolio data
   */
  exportPortfolio() {
    return {
      portfolio: this.portfolio,
      stats: this.getPortfolioStats(),
      exportedAt: new Date().toISOString(),
      version: APP_CONFIG.VERSION,
    };
  }

  /**
   * Import portfolio data
   * @param {Object} data - Portfolio data to import
   * @returns {Object} Result object with success status
   */
  importPortfolio(data) {
    try {
      if (!data || !Array.isArray(data.portfolio)) {
        return { success: false, message: 'Invalid portfolio data' };
      }

      // Validate portfolio items
      const validItems = data.portfolio.filter(item => 
        item.id && item.symbol && item.name && 
        typeof item.amount === 'number' && item.amount > 0
      );

      if (validItems.length === 0) {
        return { success: false, message: 'No valid portfolio items found' };
      }

      this.portfolio = validItems;
      this.notifyListeners();

      return { 
        success: true, 
        message: `Imported ${validItems.length} portfolio items successfully` 
      };
    } catch (error) {
      console.error('Error importing portfolio:', error);
      return { success: false, message: 'Failed to import portfolio data' };
    }
  }
}

// Create and export a singleton instance
const portfolioService = new PortfolioService();
export default portfolioService;
