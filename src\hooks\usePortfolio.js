import { useState, useEffect, useCallback, useRef } from 'react';
import { usePortfolioStorage } from './useLocalStorage.js';
import portfolioService from '../services/portfolioService.js';
import { LOADING_STATES, SUCCESS_MESSAGES } from '../utils/constants.js';

/**
 * Custom hook for managing portfolio state and operations
 * @returns {Object} Portfolio state and methods
 */
export const usePortfolio = () => {
  const [portfolio, setPortfolioStorage, clearPortfolioStorage, storageError] = usePortfolioStorage();
  const [loading, setLoading] = useState(LOADING_STATES.IDLE);
  const [error, setError] = useState(null);
  const [message, setMessage] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);
  
  const portfolioRef = useRef(portfolio);
  const messageTimeoutRef = useRef(null);

  // Update ref when portfolio changes
  useEffect(() => {
    portfolioRef.current = portfolio;
  }, [portfolio]);

  // Initialize portfolio service with stored data
  useEffect(() => {
    if (Array.isArray(portfolio)) {
      portfolioService.setPortfolio(portfolio);
      if (portfolio.length > 0) {
        setLastUpdated(new Date().toISOString());
      }
    }
  }, [portfolio]);

  // Listen for portfolio service changes
  useEffect(() => {
    const handlePortfolioChange = (newPortfolio) => {
      setPortfolioStorage(newPortfolio);
      setLastUpdated(new Date().toISOString());
    };

    portfolioService.addListener(handlePortfolioChange);
    return () => portfolioService.removeListener(handlePortfolioChange);
  }, [setPortfolioStorage]);

  // Clear message after timeout
  const clearMessage = useCallback(() => {
    if (messageTimeoutRef.current) {
      clearTimeout(messageTimeoutRef.current);
    }
    messageTimeoutRef.current = setTimeout(() => {
      setMessage(null);
    }, 5000);
  }, []);

  // Show message with auto-clear
  const showMessage = useCallback((msg, isError = false) => {
    if (isError) {
      setError(msg);
      setMessage(null);
    } else {
      setMessage(msg);
      setError(null);
    }
    clearMessage();
  }, [clearMessage]);

  // Add cryptocurrency to portfolio
  const addCoin = useCallback(async (coinData, amount) => {
    try {
      setLoading(LOADING_STATES.LOADING);
      setError(null);

      const result = portfolioService.addCoin(coinData, amount);
      
      if (result.success) {
        showMessage(result.message);
        setLoading(LOADING_STATES.SUCCESS);
      } else {
        showMessage(result.message, true);
        setLoading(LOADING_STATES.ERROR);
      }

      return result;
    } catch (error) {
      console.error('Error in addCoin:', error);
      showMessage('Failed to add cryptocurrency', true);
      setLoading(LOADING_STATES.ERROR);
      return { success: false, message: 'Failed to add cryptocurrency' };
    }
  }, [showMessage]);

  // Update holdings for existing cryptocurrency
  const updateHoldings = useCallback(async (coinId, newAmount) => {
    try {
      setLoading(LOADING_STATES.LOADING);
      setError(null);

      const result = portfolioService.updateHoldings(coinId, newAmount);
      
      if (result.success) {
        showMessage(result.message);
        setLoading(LOADING_STATES.SUCCESS);
      } else {
        showMessage(result.message, true);
        setLoading(LOADING_STATES.ERROR);
      }

      return result;
    } catch (error) {
      console.error('Error in updateHoldings:', error);
      showMessage('Failed to update holdings', true);
      setLoading(LOADING_STATES.ERROR);
      return { success: false, message: 'Failed to update holdings' };
    }
  }, [showMessage]);

  // Remove cryptocurrency from portfolio
  const removeCoin = useCallback(async (coinId) => {
    try {
      setLoading(LOADING_STATES.LOADING);
      setError(null);

      const result = portfolioService.removeCoin(coinId);
      
      if (result.success) {
        showMessage(result.message);
        setLoading(LOADING_STATES.SUCCESS);
      } else {
        showMessage(result.message, true);
        setLoading(LOADING_STATES.ERROR);
      }

      return result;
    } catch (error) {
      console.error('Error in removeCoin:', error);
      showMessage('Failed to remove cryptocurrency', true);
      setLoading(LOADING_STATES.ERROR);
      return { success: false, message: 'Failed to remove cryptocurrency' };
    }
  }, [showMessage]);

  // Update prices for all cryptocurrencies
  const updatePrices = useCallback(async (priceData) => {
    try {
      const result = portfolioService.updatePrices(priceData);
      
      if (result.success && result.updatedCount > 0) {
        setLastUpdated(new Date().toISOString());
      }

      return result;
    } catch (error) {
      console.error('Error in updatePrices:', error);
      return { success: false, message: 'Failed to update prices' };
    }
  }, []);

  // Clear entire portfolio
  const clearPortfolio = useCallback(async () => {
    try {
      setLoading(LOADING_STATES.LOADING);
      setError(null);

      const result = portfolioService.clearPortfolio();
      
      if (result.success) {
        showMessage(result.message);
        setLastUpdated(null);
        setLoading(LOADING_STATES.SUCCESS);
      } else {
        showMessage(result.message, true);
        setLoading(LOADING_STATES.ERROR);
      }

      return result;
    } catch (error) {
      console.error('Error in clearPortfolio:', error);
      showMessage('Failed to clear portfolio', true);
      setLoading(LOADING_STATES.ERROR);
      return { success: false, message: 'Failed to clear portfolio' };
    }
  }, [showMessage]);

  // Get portfolio statistics
  const getStats = useCallback(() => {
    return portfolioService.getPortfolioStats();
  }, []);

  // Get portfolio with allocation data
  const getPortfolioWithAllocation = useCallback(() => {
    return portfolioService.getPortfolioWithAllocation();
  }, []);

  // Get sorted portfolio
  const getSortedPortfolio = useCallback((sortBy, order) => {
    return portfolioService.getSortedPortfolio(sortBy, order);
  }, []);

  // Check if coin exists in portfolio
  const hasCoin = useCallback((coinId) => {
    return portfolioService.hasCoin(coinId);
  }, []);

  // Get specific portfolio item
  const getPortfolioItem = useCallback((coinId) => {
    return portfolioService.getPortfolioItem(coinId);
  }, []);

  // Export portfolio
  const exportPortfolio = useCallback(() => {
    return portfolioService.exportPortfolio();
  }, []);

  // Import portfolio
  const importPortfolio = useCallback(async (data) => {
    try {
      setLoading(LOADING_STATES.LOADING);
      setError(null);

      const result = portfolioService.importPortfolio(data);
      
      if (result.success) {
        showMessage(result.message);
        setLoading(LOADING_STATES.SUCCESS);
      } else {
        showMessage(result.message, true);
        setLoading(LOADING_STATES.ERROR);
      }

      return result;
    } catch (error) {
      console.error('Error in importPortfolio:', error);
      showMessage('Failed to import portfolio', true);
      setLoading(LOADING_STATES.ERROR);
      return { success: false, message: 'Failed to import portfolio' };
    }
  }, [showMessage]);

  // Clear messages manually
  const clearMessages = useCallback(() => {
    setMessage(null);
    setError(null);
    if (messageTimeoutRef.current) {
      clearTimeout(messageTimeoutRef.current);
    }
  }, []);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (messageTimeoutRef.current) {
        clearTimeout(messageTimeoutRef.current);
      }
    };
  }, []);

  return {
    // State
    portfolio: portfolioRef.current,
    loading,
    error: error || storageError,
    message,
    lastUpdated,
    isEmpty: !Array.isArray(portfolio) || portfolio.length === 0,
    
    // Actions
    addCoin,
    updateHoldings,
    removeCoin,
    updatePrices,
    clearPortfolio,
    clearMessages,
    
    // Getters
    getStats,
    getPortfolioWithAllocation,
    getSortedPortfolio,
    hasCoin,
    getPortfolioItem,
    
    // Import/Export
    exportPortfolio,
    importPortfolio,
    
    // Computed values
    totalValue: portfolioService.getTotalValue(),
    portfolioChange: portfolioService.getPortfolioChange(),
    diversificationScore: portfolioService.getDiversificationScore(),
  };
};
