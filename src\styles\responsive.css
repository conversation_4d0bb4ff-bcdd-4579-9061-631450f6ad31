/* Responsive Design System */

/* Breakpoint Variables */
:root {
  --breakpoint-xs: 475px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* Container Utilities */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

/* Mobile-First Grid System */
.grid-responsive {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .grid-responsive {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .grid-responsive {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

@media (min-width: 1280px) {
  .grid-responsive {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Responsive Typography */
.text-responsive-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.text-responsive-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-responsive-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-responsive-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-responsive-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

@media (min-width: 640px) {
  .text-responsive-lg {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
  
  .text-responsive-xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }
}

@media (min-width: 768px) {
  .text-responsive-xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}

/* Responsive Spacing */
.spacing-responsive {
  padding: 1rem;
}

@media (min-width: 640px) {
  .spacing-responsive {
    padding: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .spacing-responsive {
    padding: 2rem;
  }
}

/* Mobile Navigation */
.mobile-nav {
  display: block;
}

.desktop-nav {
  display: none;
}

@media (min-width: 768px) {
  .mobile-nav {
    display: none;
  }
  
  .desktop-nav {
    display: block;
  }
}

/* Responsive Cards */
.card-responsive {
  padding: 1rem;
  margin-bottom: 1rem;
}

@media (min-width: 640px) {
  .card-responsive {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .card-responsive {
    padding: 2rem;
    margin-bottom: 2rem;
  }
}

/* Responsive Tables */
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

@media (max-width: 767px) {
  .table-mobile {
    display: block;
  }
  
  .table-mobile thead {
    display: none;
  }
  
  .table-mobile tbody {
    display: block;
  }
  
  .table-mobile tr {
    display: block;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    padding: 1rem;
  }
  
  .table-mobile td {
    display: block;
    text-align: left;
    border: none;
    padding: 0.25rem 0;
  }
  
  .table-mobile td:before {
    content: attr(data-label) ": ";
    font-weight: 600;
    color: #374151;
  }
}

/* Responsive Modals */
.modal-responsive {
  margin: 1rem;
  max-width: calc(100vw - 2rem);
  max-height: calc(100vh - 2rem);
}

@media (min-width: 640px) {
  .modal-responsive {
    margin: 2rem;
    max-width: 32rem;
  }
}

@media (min-width: 768px) {
  .modal-responsive {
    max-width: 42rem;
  }
}

/* Responsive Charts */
.chart-responsive {
  width: 100%;
  height: 250px;
}

@media (min-width: 640px) {
  .chart-responsive {
    height: 300px;
  }
}

@media (min-width: 1024px) {
  .chart-responsive {
    height: 400px;
  }
}

/* Portfolio Responsive Layout */
.portfolio-layout {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 1024px) {
  .portfolio-layout {
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
  }
}

.portfolio-main {
  order: 2;
}

.portfolio-sidebar {
  order: 1;
}

@media (min-width: 1024px) {
  .portfolio-main {
    order: 1;
  }
  
  .portfolio-sidebar {
    order: 2;
  }
}

/* Responsive Coin Grid */
.coin-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .coin-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .coin-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .coin-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Responsive Metrics */
.metrics-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .metrics-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Touch-Friendly Interactions */
@media (hover: none) and (pointer: coarse) {
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
  
  .btn {
    min-height: 44px;
    padding: 0.75rem 1rem;
  }
  
  .input {
    min-height: 44px;
    padding: 0.75rem;
  }
}

/* Landscape Orientation */
@media (orientation: landscape) and (max-height: 500px) {
  .landscape-compact {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
  
  .modal-responsive {
    max-height: calc(100vh - 1rem);
    margin: 0.5rem;
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .high-dpi-border {
    border-width: 0.5px;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Dark Mode Support (Future Enhancement) */
@media (prefers-color-scheme: dark) {
  .dark-mode-ready {
    /* Dark mode styles will be added here in future updates */
  }
}

/* Print Responsive */
@media print {
  .print-responsive {
    width: 100% !important;
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  
  .print-hide {
    display: none !important;
  }
  
  .print-show {
    display: block !important;
  }
}
