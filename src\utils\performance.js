import { logPerformanceIssue } from './errorLogger.js';

/**
 * Performance Monitoring Utility
 * Tracks and reports application performance metrics
 */
class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.observers = new Map();
    this.thresholds = {
      slowAPICall: 5000, // 5 seconds
      slowRender: 100, // 100ms
      largeBundle: 1000000, // 1MB
      memoryUsage: 50000000, // 50MB
    };
    
    this.initializeObservers();
  }

  /**
   * Initialize performance observers
   */
  initializeObservers() {
    // Performance Observer for navigation timing
    if ('PerformanceObserver' in window) {
      try {
        const navObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordNavigationTiming(entry);
          }
        });
        navObserver.observe({ entryTypes: ['navigation'] });
        this.observers.set('navigation', navObserver);
      } catch (error) {
        console.warn('Navigation timing observer not supported:', error);
      }

      // Performance Observer for resource timing
      try {
        const resourceObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordResourceTiming(entry);
          }
        });
        resourceObserver.observe({ entryTypes: ['resource'] });
        this.observers.set('resource', resourceObserver);
      } catch (error) {
        console.warn('Resource timing observer not supported:', error);
      }

      // Performance Observer for largest contentful paint
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordLCP(entry);
          }
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.set('lcp', lcpObserver);
      } catch (error) {
        console.warn('LCP observer not supported:', error);
      }

      // Performance Observer for first input delay
      try {
        const fidObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordFID(entry);
          }
        });
        fidObserver.observe({ entryTypes: ['first-input'] });
        this.observers.set('fid', fidObserver);
      } catch (error) {
        console.warn('FID observer not supported:', error);
      }
    }

    // Monitor memory usage periodically
    this.startMemoryMonitoring();
  }

  /**
   * Record navigation timing metrics
   * @param {PerformanceNavigationTiming} entry - Navigation timing entry
   */
  recordNavigationTiming(entry) {
    const metrics = {
      domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
      loadComplete: entry.loadEventEnd - entry.loadEventStart,
      domInteractive: entry.domInteractive - entry.navigationStart,
      firstPaint: this.getFirstPaint(),
      firstContentfulPaint: this.getFirstContentfulPaint(),
      timeToInteractive: this.calculateTTI(entry),
    };

    this.metrics.set('navigation', metrics);
    
    // Check for performance issues
    if (metrics.domContentLoaded > 3000) {
      logPerformanceIssue('Slow DOM content loaded', { duration: metrics.domContentLoaded });
    }
    
    if (metrics.loadComplete > 5000) {
      logPerformanceIssue('Slow page load', { duration: metrics.loadComplete });
    }
  }

  /**
   * Record resource timing metrics
   * @param {PerformanceResourceTiming} entry - Resource timing entry
   */
  recordResourceTiming(entry) {
    const duration = entry.responseEnd - entry.requestStart;
    const size = entry.transferSize || 0;
    
    // Track API calls
    if (entry.name.includes('api.coingecko.com')) {
      this.recordAPITiming(entry.name, duration, size);
    }
    
    // Track large resources
    if (size > this.thresholds.largeBundle) {
      logPerformanceIssue('Large resource loaded', {
        resource: entry.name,
        size,
        duration,
      });
    }
    
    // Track slow resources
    if (duration > this.thresholds.slowAPICall && entry.name.includes('api')) {
      logPerformanceIssue('Slow API call', {
        url: entry.name,
        duration,
        size,
      });
    }
  }

  /**
   * Record API call timing
   * @param {string} url - API URL
   * @param {number} duration - Request duration
   * @param {number} size - Response size
   */
  recordAPITiming(url, duration, size) {
    const apiMetrics = this.metrics.get('api') || [];
    apiMetrics.push({
      url,
      duration,
      size,
      timestamp: Date.now(),
    });
    
    // Keep only last 100 API calls
    if (apiMetrics.length > 100) {
      apiMetrics.splice(0, apiMetrics.length - 100);
    }
    
    this.metrics.set('api', apiMetrics);
  }

  /**
   * Record Largest Contentful Paint
   * @param {PerformancePaintTiming} entry - LCP entry
   */
  recordLCP(entry) {
    const lcp = entry.startTime;
    this.metrics.set('lcp', lcp);
    
    if (lcp > 2500) {
      logPerformanceIssue('Poor LCP score', { lcp });
    }
  }

  /**
   * Record First Input Delay
   * @param {PerformanceEventTiming} entry - FID entry
   */
  recordFID(entry) {
    const fid = entry.processingStart - entry.startTime;
    this.metrics.set('fid', fid);
    
    if (fid > 100) {
      logPerformanceIssue('Poor FID score', { fid });
    }
  }

  /**
   * Get First Paint timing
   * @returns {number} First paint time
   */
  getFirstPaint() {
    const paintEntries = performance.getEntriesByType('paint');
    const fpEntry = paintEntries.find(entry => entry.name === 'first-paint');
    return fpEntry ? fpEntry.startTime : 0;
  }

  /**
   * Get First Contentful Paint timing
   * @returns {number} First contentful paint time
   */
  getFirstContentfulPaint() {
    const paintEntries = performance.getEntriesByType('paint');
    const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint');
    return fcpEntry ? fcpEntry.startTime : 0;
  }

  /**
   * Calculate Time to Interactive (simplified)
   * @param {PerformanceNavigationTiming} entry - Navigation entry
   * @returns {number} TTI estimate
   */
  calculateTTI(entry) {
    // Simplified TTI calculation
    return Math.max(
      entry.domContentLoadedEventEnd,
      this.getFirstContentfulPaint()
    );
  }

  /**
   * Start monitoring memory usage
   */
  startMemoryMonitoring() {
    if ('memory' in performance) {
      setInterval(() => {
        const memory = performance.memory;
        const memoryMetrics = {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit,
          timestamp: Date.now(),
        };
        
        this.metrics.set('memory', memoryMetrics);
        
        // Check for memory issues
        if (memory.usedJSHeapSize > this.thresholds.memoryUsage) {
          logPerformanceIssue('High memory usage', memoryMetrics);
        }
      }, 30000); // Check every 30 seconds
    }
  }

  /**
   * Measure function execution time
   * @param {string} name - Measurement name
   * @param {Function} fn - Function to measure
   * @returns {Promise|any} Function result
   */
  async measure(name, fn) {
    const startTime = performance.now();
    
    try {
      const result = await fn();
      const duration = performance.now() - startTime;
      
      this.recordCustomTiming(name, duration);
      
      if (duration > this.thresholds.slowRender) {
        logPerformanceIssue(`Slow operation: ${name}`, { duration });
      }
      
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      this.recordCustomTiming(`${name}_error`, duration);
      throw error;
    }
  }

  /**
   * Record custom timing measurement
   * @param {string} name - Measurement name
   * @param {number} duration - Duration in milliseconds
   */
  recordCustomTiming(name, duration) {
    const customMetrics = this.metrics.get('custom') || {};
    if (!customMetrics[name]) {
      customMetrics[name] = [];
    }
    
    customMetrics[name].push({
      duration,
      timestamp: Date.now(),
    });
    
    // Keep only last 50 measurements per metric
    if (customMetrics[name].length > 50) {
      customMetrics[name].splice(0, customMetrics[name].length - 50);
    }
    
    this.metrics.set('custom', customMetrics);
  }

  /**
   * Get performance summary
   * @returns {Object} Performance metrics summary
   */
  getPerformanceSummary() {
    const summary = {};
    
    for (const [key, value] of this.metrics.entries()) {
      summary[key] = value;
    }
    
    // Add Web Vitals scores
    summary.webVitals = this.getWebVitalsScores();
    
    return summary;
  }

  /**
   * Get Web Vitals scores
   * @returns {Object} Web Vitals metrics
   */
  getWebVitalsScores() {
    const lcp = this.metrics.get('lcp') || 0;
    const fid = this.metrics.get('fid') || 0;
    const cls = this.getCLS();
    
    return {
      lcp: {
        value: lcp,
        score: lcp <= 2500 ? 'good' : lcp <= 4000 ? 'needs-improvement' : 'poor',
      },
      fid: {
        value: fid,
        score: fid <= 100 ? 'good' : fid <= 300 ? 'needs-improvement' : 'poor',
      },
      cls: {
        value: cls,
        score: cls <= 0.1 ? 'good' : cls <= 0.25 ? 'needs-improvement' : 'poor',
      },
    };
  }

  /**
   * Calculate Cumulative Layout Shift (simplified)
   * @returns {number} CLS score
   */
  getCLS() {
    // This is a simplified implementation
    // In a real app, you'd use a proper CLS observer
    return 0;
  }

  /**
   * Export performance data
   * @returns {string} JSON string of performance data
   */
  exportPerformanceData() {
    return JSON.stringify({
      metrics: Object.fromEntries(this.metrics),
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      connection: this.getConnectionInfo(),
    }, null, 2);
  }

  /**
   * Get connection information
   * @returns {Object} Connection details
   */
  getConnectionInfo() {
    const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
    
    if (connection) {
      return {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt,
        saveData: connection.saveData,
      };
    }
    
    return { available: false };
  }

  /**
   * Cleanup observers
   */
  cleanup() {
    for (const observer of this.observers.values()) {
      observer.disconnect();
    }
    this.observers.clear();
  }
}

// Create and export singleton instance
const performanceMonitor = new PerformanceMonitor();

export default performanceMonitor;

// Export convenience methods
export const measure = (name, fn) => performanceMonitor.measure(name, fn);
export const getPerformanceSummary = () => performanceMonitor.getPerformanceSummary();
export const exportPerformanceData = () => performanceMonitor.exportPerformanceData();
