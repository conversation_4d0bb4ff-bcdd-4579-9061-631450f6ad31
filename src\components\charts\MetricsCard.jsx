import React from 'react';
import { TrendingUp, TrendingDown, Minus, DollarSign, PieChart, Target } from 'lucide-react';
import { formatCurrency, formatPercentage, formatNumber } from '../../utils/formatters.js';

/**
 * Metrics Card Component
 * Displays key portfolio metrics in a card format
 */
const MetricsCard = ({ 
  title,
  value,
  change,
  changePercentage,
  icon: Icon,
  type = 'currency',
  className = "",
  size = 'medium',
  showTrend = true,
  subtitle,
}) => {
  // Format value based on type
  const formatValue = (val) => {
    switch (type) {
      case 'currency':
        return formatCurrency(val);
      case 'percentage':
        return formatPercentage(val);
      case 'number':
        return formatNumber(val);
      default:
        return val?.toString() || '0';
    }
  };

  // Determine trend direction and color
  const getTrendInfo = () => {
    if (change === undefined || change === null) {
      return { icon: Minus, color: 'text-gray-500', bgColor: 'bg-gray-100' };
    }

    if (change > 0) {
      return { icon: TrendingUp, color: 'text-green-600', bgColor: 'bg-green-100' };
    } else if (change < 0) {
      return { icon: TrendingDown, color: 'text-red-600', bgColor: 'bg-red-100' };
    } else {
      return { icon: Minus, color: 'text-gray-500', bgColor: 'bg-gray-100' };
    }
  };

  const trendInfo = getTrendInfo();
  const TrendIcon = trendInfo.icon;

  // Size classes
  const sizeClasses = {
    small: {
      container: 'p-4',
      title: 'text-sm',
      value: 'text-lg',
      icon: 'w-8 h-8',
      trend: 'text-xs',
    },
    medium: {
      container: 'p-6',
      title: 'text-sm',
      value: 'text-2xl',
      icon: 'w-10 h-10',
      trend: 'text-sm',
    },
    large: {
      container: 'p-8',
      title: 'text-base',
      value: 'text-3xl',
      icon: 'w-12 h-12',
      trend: 'text-base',
    },
  };

  const classes = sizeClasses[size] || sizeClasses.medium;

  return (
    <div className={`metrics-card bg-white rounded-lg shadow-sm border border-gray-200 ${classes.container} ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          {Icon && (
            <div className={`${trendInfo.bgColor} p-2 rounded-lg`}>
              <Icon className={`${classes.icon} ${trendInfo.color}`} />
            </div>
          )}
          <div>
            <h3 className={`font-medium text-gray-900 ${classes.title}`}>
              {title}
            </h3>
            {subtitle && (
              <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
            )}
          </div>
        </div>
        
        {showTrend && change !== undefined && change !== null && (
          <div className={`flex items-center space-x-1 ${trendInfo.color}`}>
            <TrendIcon className="w-4 h-4" />
            <span className={`font-medium ${classes.trend}`}>
              {change >= 0 ? '+' : ''}{formatCurrency(change)}
            </span>
          </div>
        )}
      </div>

      {/* Value */}
      <div className="mb-2">
        <div className={`font-bold text-gray-900 ${classes.value}`}>
          {formatValue(value)}
        </div>
      </div>

      {/* Change percentage */}
      {showTrend && changePercentage !== undefined && changePercentage !== null && (
        <div className="flex items-center space-x-2">
          <span className={`${classes.trend} font-medium ${trendInfo.color}`}>
            {changePercentage >= 0 ? '+' : ''}{formatPercentage(changePercentage)}
          </span>
          <span className="text-xs text-gray-500">24h</span>
        </div>
      )}
    </div>
  );
};

/**
 * Portfolio Metrics Grid Component
 * Displays multiple metrics in a grid layout
 */
export const MetricsGrid = ({ stats, className = "" }) => {
  if (!stats) {
    return (
      <div className={`metrics-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 ${className}`}>
        {[1, 2, 3, 4].map(i => (
          <div key={i} className="bg-gray-100 rounded-lg p-6 animate-pulse">
            <div className="h-4 bg-gray-300 rounded mb-4"></div>
            <div className="h-8 bg-gray-300 rounded mb-2"></div>
            <div className="h-3 bg-gray-300 rounded w-1/2"></div>
          </div>
        ))}
      </div>
    );
  }

  const {
    totalValue = 0,
    totalChange = { changeAmount: 0, changePercentage: 0 },
    totalItems = 0,
    diversificationScore = 0,
    topPerformer = null,
    worstPerformer = null,
  } = stats;

  return (
    <div className={`metrics-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 ${className}`}>
      {/* Total Portfolio Value */}
      <MetricsCard
        title="Total Value"
        value={totalValue}
        change={totalChange.changeAmount}
        changePercentage={totalChange.changePercentage}
        icon={DollarSign}
        type="currency"
      />

      {/* Total Assets */}
      <MetricsCard
        title="Total Assets"
        value={totalItems}
        icon={PieChart}
        type="number"
        showTrend={false}
        subtitle={`${totalItems} cryptocurrencies`}
      />

      {/* Diversification Score */}
      <MetricsCard
        title="Diversification"
        value={diversificationScore}
        icon={Target}
        type="percentage"
        showTrend={false}
        subtitle="Portfolio balance"
      />

      {/* Top Performer */}
      <MetricsCard
        title="Top Performer"
        value={topPerformer?.priceChange24h || 0}
        icon={TrendingUp}
        type="percentage"
        showTrend={false}
        subtitle={topPerformer ? `${topPerformer.name} (${topPerformer.symbol.toUpperCase()})` : 'No data'}
      />
    </div>
  );
};

/**
 * Compact Metrics Row Component
 * Displays metrics in a horizontal row format
 */
export const MetricsRow = ({ stats, className = "" }) => {
  if (!stats) {
    return (
      <div className={`metrics-row flex space-x-4 overflow-x-auto ${className}`}>
        {[1, 2, 3, 4].map(i => (
          <div key={i} className="bg-gray-100 rounded-lg p-4 min-w-[200px] animate-pulse">
            <div className="h-3 bg-gray-300 rounded mb-2"></div>
            <div className="h-6 bg-gray-300 rounded mb-1"></div>
            <div className="h-2 bg-gray-300 rounded w-1/2"></div>
          </div>
        ))}
      </div>
    );
  }

  const {
    totalValue = 0,
    totalChange = { changeAmount: 0, changePercentage: 0 },
    totalItems = 0,
    diversificationScore = 0,
  } = stats;

  return (
    <div className={`metrics-row flex space-x-4 overflow-x-auto ${className}`}>
      <MetricsCard
        title="Portfolio Value"
        value={totalValue}
        change={totalChange.changeAmount}
        changePercentage={totalChange.changePercentage}
        icon={DollarSign}
        type="currency"
        size="small"
      />

      <MetricsCard
        title="Assets"
        value={totalItems}
        icon={PieChart}
        type="number"
        size="small"
        showTrend={false}
      />

      <MetricsCard
        title="Diversification"
        value={diversificationScore}
        icon={Target}
        type="percentage"
        size="small"
        showTrend={false}
      />
    </div>
  );
};

export default MetricsCard;
