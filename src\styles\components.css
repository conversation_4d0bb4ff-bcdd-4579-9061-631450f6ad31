/* Component-specific styles */

/* Button Components */
.btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply btn bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 active:bg-blue-800;
}

.btn-secondary {
  @apply btn bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 active:bg-gray-300;
}

.btn-success {
  @apply btn bg-green-600 text-white hover:bg-green-700 focus:ring-green-500 active:bg-green-800;
}

.btn-danger {
  @apply btn bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 active:bg-red-800;
}

.btn-outline {
  @apply btn border-2 border-gray-300 bg-transparent text-gray-700 hover:bg-gray-50 focus:ring-gray-500;
}

.btn-ghost {
  @apply btn bg-transparent text-gray-600 hover:bg-gray-100 focus:ring-gray-500;
}

.btn-sm {
  @apply px-3 py-1.5 text-xs;
}

.btn-lg {
  @apply px-6 py-3 text-base;
}

.btn-xl {
  @apply px-8 py-4 text-lg;
}

/* Input Components */
.input {
  @apply block w-full px-3 py-2 text-sm border border-gray-300 rounded-lg bg-white placeholder-gray-400 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500;
}

.input-error {
  @apply border-red-300 focus:ring-red-500 focus:border-transparent;
}

.input-success {
  @apply border-green-300 focus:ring-green-500 focus:border-transparent;
}

.input-lg {
  @apply px-4 py-3 text-base;
}

.input-sm {
  @apply px-2 py-1 text-xs;
}

/* Card Components */
.card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
}

.card-hover {
  @apply card transition-shadow duration-200 hover:shadow-md;
}

.card-interactive {
  @apply card-hover cursor-pointer transform transition-transform duration-200 hover:scale-[1.02] active:scale-[0.98];
}

.card-header {
  @apply px-6 py-4 border-b border-gray-200 bg-gray-50;
}

.card-body {
  @apply px-6 py-4;
}

.card-footer {
  @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
}

/* Badge Components */
.badge {
  @apply inline-flex items-center px-2.5 py-0.5 text-xs font-medium rounded-full;
}

.badge-primary {
  @apply badge bg-blue-100 text-blue-800;
}

.badge-success {
  @apply badge bg-green-100 text-green-800;
}

.badge-danger {
  @apply badge bg-red-100 text-red-800;
}

.badge-warning {
  @apply badge bg-yellow-100 text-yellow-800;
}

.badge-gray {
  @apply badge bg-gray-100 text-gray-800;
}

.badge-lg {
  @apply px-3 py-1 text-sm;
}

/* Alert Components */
.alert {
  @apply p-4 rounded-lg border;
}

.alert-info {
  @apply alert bg-blue-50 border-blue-200 text-blue-800;
}

.alert-success {
  @apply alert bg-green-50 border-green-200 text-green-800;
}

.alert-warning {
  @apply alert bg-yellow-50 border-yellow-200 text-yellow-800;
}

.alert-danger {
  @apply alert bg-red-50 border-red-200 text-red-800;
}

/* Modal Components */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50;
}

.modal-content {
  @apply bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-hidden;
}

.modal-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200;
}

.modal-body {
  @apply p-6 overflow-y-auto;
}

.modal-footer {
  @apply flex items-center justify-end space-x-3 p-6 border-t border-gray-200;
}

/* Dropdown Components */
.dropdown {
  @apply relative inline-block text-left;
}

.dropdown-menu {
  @apply absolute right-0 mt-2 w-56 bg-white border border-gray-200 rounded-lg shadow-lg z-50 py-1;
}

.dropdown-item {
  @apply block w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-150;
}

.dropdown-divider {
  @apply border-t border-gray-200 my-1;
}

/* Table Components */
.table {
  @apply min-w-full divide-y divide-gray-200;
}

.table-header {
  @apply bg-gray-50;
}

.table-header-cell {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table-body {
  @apply bg-white divide-y divide-gray-200;
}

.table-row {
  @apply hover:bg-gray-50 transition-colors duration-150;
}

.table-cell {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

/* Loading Components */
.skeleton {
  @apply animate-pulse bg-gray-200 rounded;
}

.skeleton-text {
  @apply skeleton h-4;
}

.skeleton-title {
  @apply skeleton h-6;
}

.skeleton-avatar {
  @apply skeleton w-10 h-10 rounded-full;
}

.skeleton-button {
  @apply skeleton h-10 w-24;
}

/* Chart Components */
.chart-container {
  @apply relative w-full;
}

.chart-legend {
  @apply flex flex-wrap justify-center mt-4 space-x-4;
}

.chart-legend-item {
  @apply flex items-center space-x-2 text-sm;
}

.chart-legend-color {
  @apply w-3 h-3 rounded-full;
}

/* Portfolio Components */
.portfolio-card {
  @apply card-hover p-6;
}

.portfolio-header {
  @apply flex items-center justify-between mb-4;
}

.portfolio-stats {
  @apply grid grid-cols-2 md:grid-cols-4 gap-4;
}

.portfolio-stat {
  @apply text-center;
}

.portfolio-stat-value {
  @apply text-2xl font-bold text-gray-900;
}

.portfolio-stat-label {
  @apply text-sm text-gray-600;
}

.portfolio-stat-change {
  @apply text-sm font-medium;
}

.portfolio-stat-change.positive {
  @apply text-green-600;
}

.portfolio-stat-change.negative {
  @apply text-red-600;
}

/* Coin Components */
.coin-card {
  @apply portfolio-card;
}

.coin-header {
  @apply flex items-center space-x-3 mb-4;
}

.coin-avatar {
  @apply w-12 h-12 rounded-full;
}

.coin-info {
  @apply flex-1;
}

.coin-name {
  @apply text-lg font-semibold text-gray-900;
}

.coin-symbol {
  @apply text-sm text-gray-500 uppercase;
}

.coin-price {
  @apply text-xl font-bold text-gray-900;
}

.coin-change {
  @apply text-sm font-medium;
}

.coin-change.positive {
  @apply text-green-600;
}

.coin-change.negative {
  @apply text-red-600;
}

/* Responsive Utilities */
@media (max-width: 640px) {
  .mobile-stack {
    @apply flex-col space-y-2 space-x-0;
  }
  
  .mobile-full {
    @apply w-full;
  }
  
  .mobile-hidden {
    @apply hidden;
  }
}

@media (min-width: 641px) {
  .desktop-flex {
    @apply flex space-x-4 space-y-0;
  }
  
  .mobile-only {
    @apply hidden;
  }
}
