import React from 'react';
import { TrendingUp, TrendingDown, DollarSign, <PERSON><PERSON><PERSON>, Target, Clock } from 'lucide-react';
import { formatCurrency, formatPercentage, formatTimeAgo } from '../../utils/formatters.js';
import { MetricsGrid } from '../charts/MetricsCard.jsx';
import AllocationChart from '../charts/AllocationChart.jsx';

/**
 * Portfolio Summary Component
 * Displays overview of portfolio performance and allocation
 */
const PortfolioSummary = ({
  portfolio = [],
  stats = null,
  lastUpdated = null,
  isLoading = false,
  className = "",
}) => {
  // Handle loading state
  if (isLoading) {
    return (
      <div className={`portfolio-summary ${className}`}>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Metrics Loading */}
          <div className="lg:col-span-2">
            <div className="bg-gray-100 rounded-lg p-6 animate-pulse">
              <div className="h-6 bg-gray-300 rounded w-1/3 mb-4"></div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {[1, 2, 3, 4].map(i => (
                  <div key={i} className="space-y-2">
                    <div className="h-4 bg-gray-300 rounded"></div>
                    <div className="h-8 bg-gray-300 rounded"></div>
                    <div className="h-3 bg-gray-300 rounded w-2/3"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
          
          {/* Chart Loading */}
          <div className="lg:col-span-1">
            <div className="bg-gray-100 rounded-lg p-6 animate-pulse">
              <div className="h-6 bg-gray-300 rounded w-1/2 mb-4"></div>
              <div className="w-full h-64 bg-gray-300 rounded-full"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Handle empty portfolio
  if (!Array.isArray(portfolio) || portfolio.length === 0) {
    return (
      <div className={`portfolio-summary ${className}`}>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <PieChart className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No Portfolio Data
            </h3>
            <p className="text-gray-600 mb-6">
              Start building your cryptocurrency portfolio by adding your first coin.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-500">
              <div className="flex items-center justify-center space-x-2">
                <DollarSign className="w-4 h-4" />
                <span>Track real-time prices</span>
              </div>
              <div className="flex items-center justify-center space-x-2">
                <PieChart className="w-4 h-4" />
                <span>Visualize allocation</span>
              </div>
              <div className="flex items-center justify-center space-x-2">
                <Target className="w-4 h-4" />
                <span>Monitor performance</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const {
    totalValue = 0,
    totalChange = { changeAmount: 0, changePercentage: 0 },
    totalItems = 0,
    diversificationScore = 0,
    topPerformer = null,
    worstPerformer = null,
  } = stats || {};

  return (
    <div className={`portfolio-summary space-y-6 ${className}`}>
      {/* Header with Last Updated */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Portfolio Overview</h2>
          <p className="text-gray-600 mt-1">
            Track your cryptocurrency investments in real-time
          </p>
        </div>
        {lastUpdated && (
          <div className="flex items-center space-x-2 text-sm text-gray-500 mt-2 sm:mt-0">
            <Clock className="w-4 h-4" />
            <span>Updated {formatTimeAgo(lastUpdated)}</span>
          </div>
        )}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
        {/* Metrics Section */}
        <div className="xl:col-span-2 space-y-6">
          {/* Key Metrics */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Key Metrics</h3>
            <MetricsGrid stats={stats} />
          </div>

          {/* Performance Highlights */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance Highlights</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Top Performer */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <TrendingUp className="w-5 h-5 text-green-600" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-green-900">Top Performer</h4>
                    {topPerformer ? (
                      <div>
                        <p className="text-sm text-green-800">
                          {topPerformer.name} ({topPerformer.symbol.toUpperCase()})
                        </p>
                        <p className="text-lg font-bold text-green-900">
                          +{formatPercentage(topPerformer.priceChange24h)}
                        </p>
                      </div>
                    ) : (
                      <p className="text-sm text-green-700">No data available</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Worst Performer */}
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                    <TrendingDown className="w-5 h-5 text-red-600" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-red-900">Worst Performer</h4>
                    {worstPerformer ? (
                      <div>
                        <p className="text-sm text-red-800">
                          {worstPerformer.name} ({worstPerformer.symbol.toUpperCase()})
                        </p>
                        <p className="text-lg font-bold text-red-900">
                          {formatPercentage(worstPerformer.priceChange24h)}
                        </p>
                      </div>
                    ) : (
                      <p className="text-sm text-red-700">No data available</p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Allocation Chart Section */}
        <div className="xl:col-span-1">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <AllocationChart 
              portfolio={portfolio}
              title="Portfolio Allocation"
              height={300}
            />
          </div>
        </div>
      </div>

      {/* Portfolio Stats Summary */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-900">
              {formatCurrency(totalValue)}
            </div>
            <div className="text-sm text-blue-700">Total Portfolio Value</div>
            <div className={`text-sm font-medium mt-1 ${
              totalChange.changePercentage >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {totalChange.changePercentage >= 0 ? '+' : ''}{formatPercentage(totalChange.changePercentage)} (24h)
            </div>
          </div>

          <div className="text-center">
            <div className="text-2xl font-bold text-purple-900">{totalItems}</div>
            <div className="text-sm text-purple-700">Cryptocurrencies</div>
            <div className="text-sm text-purple-600 mt-1">in portfolio</div>
          </div>

          <div className="text-center">
            <div className="text-2xl font-bold text-indigo-900">
              {formatPercentage(diversificationScore)}
            </div>
            <div className="text-sm text-indigo-700">Diversification Score</div>
            <div className="text-sm text-indigo-600 mt-1">
              {diversificationScore >= 70 ? 'Well diversified' : 
               diversificationScore >= 40 ? 'Moderately diversified' : 'Low diversification'}
            </div>
          </div>

          <div className="text-center">
            <div className="text-2xl font-bold text-teal-900">
              {formatCurrency(totalChange.changeAmount)}
            </div>
            <div className="text-sm text-teal-700">24h Change</div>
            <div className={`text-sm font-medium mt-1 ${
              totalChange.changeAmount >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {totalChange.changeAmount >= 0 ? 'Profit' : 'Loss'}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PortfolioSummary;
