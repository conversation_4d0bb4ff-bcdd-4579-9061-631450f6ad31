import React, { useMemo } from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';
import { CHART_CONFIG } from '../../utils/constants.js';
import { formatCurrency, formatPercentage, formatDate } from '../../utils/formatters.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

/**
 * Portfolio Performance Chart Component
 * Displays portfolio performance over time as a line chart
 */
const PerformanceChart = ({ 
  performanceData = [], 
  title = "Portfolio Performance",
  showGrid = true,
  showPoints = false,
  height = 400,
  className = "",
  timeframe = '24h',
}) => {
  // Prepare chart data
  const chartData = useMemo(() => {
    if (!Array.isArray(performanceData) || performanceData.length === 0) {
      // Generate sample data for empty state
      const now = new Date();
      const sampleData = Array.from({ length: 24 }, (_, i) => ({
        timestamp: new Date(now.getTime() - (23 - i) * 60 * 60 * 1000).toISOString(),
        value: 0,
        change: 0,
      }));

      return {
        labels: sampleData.map(item => formatDate(item.timestamp)),
        datasets: [{
          label: 'Portfolio Value',
          data: sampleData.map(item => item.value),
          borderColor: '#9ca3af',
          backgroundColor: 'rgba(156, 163, 175, 0.1)',
          borderWidth: 2,
          fill: true,
          tension: 0.4,
          pointRadius: 0,
          pointHoverRadius: 6,
        }],
      };
    }

    const labels = performanceData.map(item => {
      const date = new Date(item.timestamp);
      switch (timeframe) {
        case '1h':
          return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        case '24h':
          return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        case '7d':
          return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
        case '30d':
          return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
        default:
          return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      }
    });

    const values = performanceData.map(item => item.value);
    const changes = performanceData.map(item => item.change || 0);

    // Determine color based on overall performance
    const firstValue = values[0] || 0;
    const lastValue = values[values.length - 1] || 0;
    const isPositive = lastValue >= firstValue;

    const borderColor = isPositive ? '#10b981' : '#ef4444';
    const backgroundColor = isPositive 
      ? 'rgba(16, 185, 129, 0.1)' 
      : 'rgba(239, 68, 68, 0.1)';

    return {
      labels,
      datasets: [{
        label: 'Portfolio Value',
        data: values,
        borderColor,
        backgroundColor,
        borderWidth: 3,
        fill: true,
        tension: 0.4,
        pointRadius: showPoints ? 4 : 0,
        pointHoverRadius: 8,
        pointBackgroundColor: borderColor,
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2,
        pointHoverBackgroundColor: borderColor,
        pointHoverBorderColor: '#ffffff',
        pointHoverBorderWidth: 3,
      }],
    };
  }, [performanceData, timeframe, showPoints]);

  // Chart options
  const options = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      title: {
        display: !!title,
        text: title,
        font: {
          size: 16,
          weight: 'bold',
        },
        padding: {
          top: 10,
          bottom: 20,
        },
      },
      legend: {
        display: false,
      },
      tooltip: {
        enabled: true,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: '#374151',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: false,
        callbacks: {
          title: (context) => {
            const index = context[0].dataIndex;
            const item = performanceData[index];
            return item ? formatDate(item.timestamp) : '';
          },
          label: (context) => {
            const index = context.dataIndex;
            const item = performanceData[index];
            if (!item) return '';

            const lines = [
              `Value: ${formatCurrency(item.value)}`,
            ];

            if (item.change !== undefined) {
              const changeIcon = item.change >= 0 ? '📈' : '📉';
              lines.push(`Change: ${changeIcon} ${formatPercentage(item.change)}`);
            }

            return lines;
          },
        },
      },
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: showGrid,
          color: 'rgba(156, 163, 175, 0.2)',
        },
        ticks: {
          color: '#6b7280',
          font: {
            size: 12,
          },
          maxTicksLimit: 8,
        },
      },
      y: {
        display: true,
        grid: {
          display: showGrid,
          color: 'rgba(156, 163, 175, 0.2)',
        },
        ticks: {
          color: '#6b7280',
          font: {
            size: 12,
          },
          callback: (value) => formatCurrency(value),
        },
        beginAtZero: false,
      },
    },
    animation: {
      duration: CHART_CONFIG.ANIMATION_DURATION,
      easing: 'easeInOutQuart',
    },
    interaction: {
      intersect: false,
      mode: 'index',
    },
    elements: {
      point: {
        hoverRadius: 8,
      },
    },
  }), [performanceData, title, showGrid, timeframe]);

  // Calculate performance stats
  const stats = useMemo(() => {
    if (!Array.isArray(performanceData) || performanceData.length === 0) {
      return {
        currentValue: 0,
        change: 0,
        changePercentage: 0,
        high: 0,
        low: 0,
      };
    }

    const values = performanceData.map(item => item.value);
    const firstValue = values[0] || 0;
    const lastValue = values[values.length - 1] || 0;
    const high = Math.max(...values);
    const low = Math.min(...values);
    
    const change = lastValue - firstValue;
    const changePercentage = firstValue !== 0 ? (change / firstValue) * 100 : 0;

    return {
      currentValue: lastValue,
      change,
      changePercentage,
      high,
      low,
    };
  }, [performanceData]);

  // Handle empty data
  if (!Array.isArray(performanceData) || performanceData.length === 0) {
    return (
      <div className={`performance-chart-container ${className}`}>
        <div className="empty-chart" style={{ height }}>
          <div className="empty-chart-content">
            <div className="empty-chart-icon">📈</div>
            <h3>No Performance Data</h3>
            <p>Performance data will appear here once you have portfolio activity</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`performance-chart-container ${className}`}>
      {/* Performance summary */}
      <div className="performance-summary">
        <div className="summary-stats">
          <div className="stat-item">
            <span className="stat-label">Current Value</span>
            <span className="stat-value">{formatCurrency(stats.currentValue)}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Change</span>
            <span className={`stat-value ${stats.change >= 0 ? 'positive' : 'negative'}`}>
              {stats.change >= 0 ? '+' : ''}{formatCurrency(stats.change)}
              <span className="change-percentage">
                ({stats.changePercentage >= 0 ? '+' : ''}{formatPercentage(stats.changePercentage)})
              </span>
            </span>
          </div>
          <div className="stat-item">
            <span className="stat-label">High</span>
            <span className="stat-value">{formatCurrency(stats.high)}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">Low</span>
            <span className="stat-value">{formatCurrency(stats.low)}</span>
          </div>
        </div>
      </div>

      {/* Chart */}
      <div className="chart-wrapper" style={{ height }}>
        <Line data={chartData} options={options} />
      </div>
    </div>
  );
};

export default PerformanceChart;
