import React, { useState, useEffect, useRef } from 'react';
import { X, Search, Plus, TrendingUp, TrendingDown } from 'lucide-react';
import { useCryptoAPI } from '../../hooks/useCryptoAPI.js';
import { formatCurrency, formatPercentage } from '../../utils/formatters.js';
import LoadingSpinner from '../common/LoadingSpinner.jsx';

/**
 * Add Coin Modal Component
 * Modal for searching and adding cryptocurrencies to portfolio
 */
const AddCoinModal = ({
  isOpen,
  onClose,
  onAddCoin,
  existingCoins = [],
  className = "",
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [selectedCoin, setSelectedCoin] = useState(null);
  const [amount, setAmount] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [isAdding, setIsAdding] = useState(false);
  const [error, setError] = useState('');

  const searchInputRef = useRef(null);
  const searchTimeoutRef = useRef(null);
  const { searchCoins, getCoinsMarketData } = useCryptoAPI();

  // Focus search input when modal opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Handle search with debouncing
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    if (searchQuery.trim().length < 2) {
      setSearchResults([]);
      return;
    }

    searchTimeoutRef.current = setTimeout(async () => {
      try {
        setIsSearching(true);
        setError('');
        const results = await searchCoins(searchQuery);
        
        // Filter out coins already in portfolio
        const existingCoinIds = existingCoins.map(coin => coin.id);
        const filteredResults = results.filter(coin => !existingCoinIds.includes(coin.id));
        
        setSearchResults(filteredResults);
      } catch (error) {
        console.error('Search error:', error);
        setError('Failed to search cryptocurrencies. Please try again.');
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    }, 300);
  }, [searchQuery, searchCoins, existingCoins]);

  // Handle coin selection
  const handleCoinSelect = async (coin) => {
    try {
      setSelectedCoin(coin);
      setError('');
      
      // Get current market data for the selected coin
      const marketData = await getCoinsMarketData([coin.id]);
      if (marketData.length > 0) {
        setSelectedCoin({
          ...coin,
          ...marketData[0],
        });
      }
    } catch (error) {
      console.error('Error fetching coin data:', error);
      setError('Failed to fetch coin data. Please try again.');
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!selectedCoin || !amount) {
      setError('Please select a cryptocurrency and enter an amount.');
      return;
    }

    const amountNum = parseFloat(amount);
    if (isNaN(amountNum) || amountNum <= 0) {
      setError('Please enter a valid amount greater than 0.');
      return;
    }

    try {
      setIsAdding(true);
      setError('');
      
      const result = await onAddCoin(selectedCoin, amountNum);
      
      if (result.success) {
        handleClose();
      } else {
        setError(result.message || 'Failed to add cryptocurrency.');
      }
    } catch (error) {
      console.error('Error adding coin:', error);
      setError('Failed to add cryptocurrency. Please try again.');
    } finally {
      setIsAdding(false);
    }
  };

  // Handle modal close
  const handleClose = () => {
    setSearchQuery('');
    setSearchResults([]);
    setSelectedCoin(null);
    setAmount('');
    setError('');
    setIsSearching(false);
    setIsAdding(false);
    onClose();
  };

  // Handle keyboard events
  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      handleClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div 
        className={`bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden ${className}`}
        onKeyDown={handleKeyDown}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Add Cryptocurrency</h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {!selectedCoin ? (
            /* Search Phase */
            <div className="space-y-4">
              {/* Search Input */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  ref={searchInputRef}
                  type="text"
                  placeholder="Search cryptocurrencies..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* Search Results */}
              <div className="space-y-2">
                {isSearching && (
                  <div className="flex items-center justify-center py-8">
                    <LoadingSpinner text="Searching..." />
                  </div>
                )}

                {!isSearching && searchQuery.length >= 2 && searchResults.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No cryptocurrencies found for "{searchQuery}"
                  </div>
                )}

                {!isSearching && searchResults.map((coin) => (
                  <button
                    key={coin.id}
                    onClick={() => handleCoinSelect(coin)}
                    className="w-full flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
                  >
                    <img
                      src={coin.thumb || coin.large}
                      alt={coin.name}
                      className="w-8 h-8 rounded-full"
                      onError={(e) => {
                        e.target.style.display = 'none';
                      }}
                    />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">{coin.name}</div>
                      <div className="text-sm text-gray-500 uppercase">{coin.symbol}</div>
                    </div>
                    {coin.market_cap_rank && (
                      <div className="text-sm text-gray-400">
                        #{coin.market_cap_rank}
                      </div>
                    )}
                  </button>
                ))}
              </div>

              {searchQuery.length < 2 && (
                <div className="text-center py-8 text-gray-500">
                  <Search className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p>Start typing to search for cryptocurrencies</p>
                </div>
              )}
            </div>
          ) : (
            /* Add Amount Phase */
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Selected Coin Info */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center space-x-3 mb-3">
                  <img
                    src={selectedCoin.image || selectedCoin.large}
                    alt={selectedCoin.name}
                    className="w-12 h-12 rounded-full"
                    onError={(e) => {
                      e.target.style.display = 'none';
                    }}
                  />
                  <div>
                    <h3 className="font-semibold text-gray-900">{selectedCoin.name}</h3>
                    <p className="text-sm text-gray-500 uppercase">{selectedCoin.symbol}</p>
                  </div>
                </div>

                {selectedCoin.current_price && (
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Current Price:</span>
                      <div className="font-medium text-gray-900">
                        {formatCurrency(selectedCoin.current_price)}
                      </div>
                    </div>
                    {selectedCoin.price_change_percentage_24h !== undefined && (
                      <div>
                        <span className="text-gray-600">24h Change:</span>
                        <div className={`font-medium flex items-center space-x-1 ${
                          selectedCoin.price_change_percentage_24h >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {selectedCoin.price_change_percentage_24h >= 0 ? (
                            <TrendingUp className="w-4 h-4" />
                          ) : (
                            <TrendingDown className="w-4 h-4" />
                          )}
                          <span>
                            {selectedCoin.price_change_percentage_24h >= 0 ? '+' : ''}
                            {formatPercentage(selectedCoin.price_change_percentage_24h)}
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Amount Input */}
              <div>
                <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-2">
                  Amount of {selectedCoin.symbol?.toUpperCase()}
                </label>
                <input
                  id="amount"
                  type="number"
                  step="any"
                  min="0"
                  placeholder="0.00"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
                {amount && selectedCoin.current_price && (
                  <p className="mt-2 text-sm text-gray-600">
                    Total value: {formatCurrency(parseFloat(amount) * selectedCoin.current_price)}
                  </p>
                )}
              </div>

              {/* Error Message */}
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="text-sm text-red-600">{error}</p>
                </div>
              )}

              {/* Actions */}
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={() => setSelectedCoin(null)}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Back to Search
                </button>
                <button
                  type="submit"
                  disabled={isAdding || !amount}
                  className="flex-1 flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isAdding ? (
                    <LoadingSpinner size="small" color="white" />
                  ) : (
                    <>
                      <Plus className="w-4 h-4 mr-2" />
                      Add to Portfolio
                    </>
                  )}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default AddCoinModal;
