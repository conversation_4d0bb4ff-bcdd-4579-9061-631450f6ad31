import React, { useState } from 'react';
import { Menu, X, Plus, RefreshCw, Settings, Download, Upload } from 'lucide-react';
import { APP_CONFIG } from '../../utils/constants.js';
import { formatTimeAgo } from '../../utils/formatters.js';

/**
 * Header Component
 * Main navigation header with responsive design
 */
const Header = ({
  onAddCoin,
  onRefresh,
  onSettings,
  onExport,
  onImport,
  isRefreshing = false,
  lastUpdated = null,
  className = "",
}) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleAction = (action) => {
    setIsMobileMenuOpen(false);
    action?.();
  };

  return (
    <header className={`header bg-white shadow-sm border-b border-gray-200 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo and Brand */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">CP</span>
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">{APP_CONFIG.NAME}</h1>
                <p className="text-xs text-gray-500 hidden sm:block">
                  Professional crypto portfolio tracker
                </p>
              </div>
            </div>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-4">
            {/* Last Updated */}
            {lastUpdated && (
              <div className="text-sm text-gray-500">
                Updated {formatTimeAgo(lastUpdated)}
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handleAction(onAddCoin)}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                <Plus className="w-4 h-4 mr-1" />
                Add Coin
              </button>

              <button
                onClick={() => handleAction(onRefresh)}
                disabled={isRefreshing}
                className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50"
              >
                <RefreshCw className={`w-4 h-4 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
                Refresh
              </button>

              {/* More Actions Dropdown */}
              <div className="relative">
                <button
                  onClick={() => handleAction(onSettings)}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                >
                  <Settings className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <button
              onClick={toggleMobileMenu}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
            >
              {isMobileMenuOpen ? (
                <X className="w-6 h-6" />
              ) : (
                <Menu className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t border-gray-200 py-4">
            <div className="space-y-3">
              {/* Last Updated - Mobile */}
              {lastUpdated && (
                <div className="px-4 py-2 text-sm text-gray-500 border-b border-gray-100">
                  Last updated {formatTimeAgo(lastUpdated)}
                </div>
              )}

              {/* Mobile Actions */}
              <button
                onClick={() => handleAction(onAddCoin)}
                className="flex items-center w-full px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
              >
                <Plus className="w-5 h-5 mr-3 text-blue-600" />
                Add Cryptocurrency
              </button>

              <button
                onClick={() => handleAction(onRefresh)}
                disabled={isRefreshing}
                className="flex items-center w-full px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50"
              >
                <RefreshCw className={`w-5 h-5 mr-3 text-gray-600 ${isRefreshing ? 'animate-spin' : ''}`} />
                Refresh Prices
              </button>

              <button
                onClick={() => handleAction(onExport)}
                className="flex items-center w-full px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
              >
                <Download className="w-5 h-5 mr-3 text-gray-600" />
                Export Portfolio
              </button>

              <button
                onClick={() => handleAction(onImport)}
                className="flex items-center w-full px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
              >
                <Upload className="w-5 h-5 mr-3 text-gray-600" />
                Import Portfolio
              </button>

              <button
                onClick={() => handleAction(onSettings)}
                className="flex items-center w-full px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
              >
                <Settings className="w-5 h-5 mr-3 text-gray-600" />
                Settings
              </button>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
