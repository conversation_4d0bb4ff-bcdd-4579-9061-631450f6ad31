import { CURRENCY_CONFIG } from './constants.js';

/**
 * Format a number as currency
 * @param {number} value - The value to format
 * @param {string} currency - Currency code (default: 'usd')
 * @param {number} minimumFractionDigits - Minimum decimal places
 * @param {number} maximumFractionDigits - Maximum decimal places
 * @returns {string} Formatted currency string
 */
export const formatCurrency = (
  value,
  currency = CURRENCY_CONFIG.DEFAULT,
  minimumFractionDigits = 2,
  maximumFractionDigits = 2
) => {
  if (value === null || value === undefined || isNaN(value)) {
    return `${CURRENCY_CONFIG.SYMBOL}0.00`;
  }

  // For very small values, show more decimal places
  if (Math.abs(value) < 0.01 && value !== 0) {
    maximumFractionDigits = 8;
    minimumFractionDigits = 2;
  }

  try {
    return new Intl.NumberFormat(CURRENCY_CONFIG.LOCALE, {
      style: 'currency',
      currency: currency.toUpperCase(),
      minimumFractionDigits,
      maximumFractionDigits,
    }).format(value);
  } catch (error) {
    // Fallback formatting
    return `${CURRENCY_CONFIG.SYMBOL}${value.toFixed(2)}`;
  }
};

/**
 * Format a number with appropriate decimal places
 * @param {number} value - The value to format
 * @param {number} decimals - Number of decimal places
 * @returns {string} Formatted number string
 */
export const formatNumber = (value, decimals = 2) => {
  if (value === null || value === undefined || isNaN(value)) {
    return '0';
  }

  // For very large numbers, use compact notation
  if (Math.abs(value) >= 1000000) {
    return new Intl.NumberFormat(CURRENCY_CONFIG.LOCALE, {
      notation: 'compact',
      maximumFractionDigits: 2,
    }).format(value);
  }

  return new Intl.NumberFormat(CURRENCY_CONFIG.LOCALE, {
    minimumFractionDigits: 0,
    maximumFractionDigits: decimals,
  }).format(value);
};

/**
 * Format percentage with appropriate styling
 * @param {number} value - The percentage value
 * @param {number} decimals - Number of decimal places
 * @returns {string} Formatted percentage string
 */
export const formatPercentage = (value, decimals = 2) => {
  if (value === null || value === undefined || isNaN(value)) {
    return '0.00%';
  }

  const formatted = value.toFixed(decimals);
  return `${formatted}%`;
};

/**
 * Format percentage with color class
 * @param {number} value - The percentage value
 * @param {number} decimals - Number of decimal places
 * @returns {object} Object with formatted value and color class
 */
export const formatPercentageWithColor = (value, decimals = 2) => {
  const formatted = formatPercentage(value, decimals);
  const colorClass = value > 0 ? 'positive' : value < 0 ? 'negative' : 'neutral';
  
  return {
    value: formatted,
    colorClass,
    isPositive: value > 0,
    isNegative: value < 0,
    isNeutral: value === 0,
  };
};

/**
 * Format cryptocurrency amount with appropriate decimal places
 * @param {number} amount - The amount to format
 * @param {string} symbol - The cryptocurrency symbol
 * @returns {string} Formatted crypto amount
 */
export const formatCryptoAmount = (amount, symbol = '') => {
  if (amount === null || amount === undefined || isNaN(amount)) {
    return `0 ${symbol}`.trim();
  }

  let decimals = 8;
  
  // Adjust decimal places based on amount size
  if (amount >= 1000) {
    decimals = 2;
  } else if (amount >= 1) {
    decimals = 4;
  } else if (amount >= 0.01) {
    decimals = 6;
  }

  const formatted = formatNumber(amount, decimals);
  return `${formatted} ${symbol}`.trim();
};

/**
 * Format market cap with appropriate units
 * @param {number} value - Market cap value
 * @returns {string} Formatted market cap
 */
export const formatMarketCap = (value) => {
  if (value === null || value === undefined || isNaN(value)) {
    return 'N/A';
  }

  if (value >= 1e12) {
    return `${CURRENCY_CONFIG.SYMBOL}${(value / 1e12).toFixed(2)}T`;
  } else if (value >= 1e9) {
    return `${CURRENCY_CONFIG.SYMBOL}${(value / 1e9).toFixed(2)}B`;
  } else if (value >= 1e6) {
    return `${CURRENCY_CONFIG.SYMBOL}${(value / 1e6).toFixed(2)}M`;
  } else if (value >= 1e3) {
    return `${CURRENCY_CONFIG.SYMBOL}${(value / 1e3).toFixed(2)}K`;
  }

  return formatCurrency(value);
};

/**
 * Format date to readable string
 * @param {string|Date} date - Date to format
 * @returns {string} Formatted date string
 */
export const formatDate = (date) => {
  if (!date) return 'N/A';

  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat(CURRENCY_CONFIG.LOCALE, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(dateObj);
  } catch (error) {
    return 'Invalid Date';
  }
};

/**
 * Format time ago (e.g., "2 hours ago")
 * @param {string|Date} date - Date to format
 * @returns {string} Time ago string
 */
export const formatTimeAgo = (date) => {
  if (!date) return 'N/A';

  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffInSeconds = Math.floor((now - dateObj) / 1000);

    if (diffInSeconds < 60) {
      return 'Just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    }
  } catch (error) {
    return 'N/A';
  }
};

/**
 * Truncate text with ellipsis
 * @param {string} text - Text to truncate
 * @param {number} maxLength - Maximum length
 * @returns {string} Truncated text
 */
export const truncateText = (text, maxLength = 50) => {
  if (!text || text.length <= maxLength) {
    return text || '';
  }
  
  return `${text.substring(0, maxLength)}...`;
};

/**
 * Format file size
 * @param {number} bytes - Size in bytes
 * @returns {string} Formatted file size
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};
